{% extends "base.html" %}

{% block title %}事件管理 - Cerberus监控系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        事件告警管理
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" onclick="refreshEvents()">
                            <i class="fas fa-sync"></i>
                            刷新
                        </button>
                        <button type="button" class="btn btn-warning btn-sm" onclick="clearAllEvents()">
                            <i class="fas fa-trash"></i>
                            清空所有
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 筛选器 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-control" id="eventTypeFilter">
                                <option value="">所有事件类型</option>
                                <option value="object_detection">目标检测</option>
                                <option value="face_recognition">人脸识别</option>
                                <option value="line_crossing">越线检测</option>
                                <option value="camera_obstruction">摄像头异常</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="severityFilter">
                                <option value="">所有严重程度</option>
                                <option value="low">低</option>
                                <option value="medium">中</option>
                                <option value="high">高</option>
                                <option value="critical">严重</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="dateFilter">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info" onclick="applyFilters()">
                                <i class="fas fa-filter"></i>
                                应用筛选
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="eventsTable">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>事件类型</th>
                                    <th>摄像头</th>
                                    <th>严重程度</th>
                                    <th>描述</th>
                                    <th>置信度</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="eventsTableBody">
                                <!-- 示例数据 -->
                                <tr>
                                    <td>2025-07-12 13:45:23</td>
                                    <td><span class="badge badge-info">目标检测</span></td>
                                    <td>前门摄像头</td>
                                    <td><span class="badge badge-warning">中</span></td>
                                    <td>检测到人员进入</td>
                                    <td>0.85</td>
                                    <td><span class="badge badge-danger">未处理</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="viewEventDetails(1)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" onclick="markAsHandled(1)">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteEvent(1)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2025-07-12 13:30:15</td>
                                    <td><span class="badge badge-warning">越线检测</span></td>
                                    <td>后门摄像头</td>
                                    <td><span class="badge badge-danger">高</span></td>
                                    <td>检测到车辆越线</td>
                                    <td>0.92</td>
                                    <td><span class="badge badge-success">已处理</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="viewEventDetails(2)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" disabled>
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteEvent(2)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2025-07-12 13:15:08</td>
                                    <td><span class="badge badge-danger">摄像头异常</span></td>
                                    <td>前门摄像头</td>
                                    <td><span class="badge badge-danger">严重</span></td>
                                    <td>摄像头信号丢失</td>
                                    <td>1.00</td>
                                    <td><span class="badge badge-success">已处理</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="viewEventDetails(3)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" disabled>
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteEvent(3)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav aria-label="事件分页">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">上一页</a>
                            </li>
                            <li class="page-item active">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">下一页</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-info">
                <div class="card-body">
                    <div class="d-flex">
                        <div>
                            <h3 class="text-white">24</h3>
                            <p class="text-white">今日事件</p>
                        </div>
                        <div class="ml-auto">
                            <i class="fas fa-calendar-day fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning">
                <div class="card-body">
                    <div class="d-flex">
                        <div>
                            <h3 class="text-white">8</h3>
                            <p class="text-white">未处理事件</p>
                        </div>
                        <div class="ml-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success">
                <div class="card-body">
                    <div class="d-flex">
                        <div>
                            <h3 class="text-white">16</h3>
                            <p class="text-white">已处理事件</p>
                        </div>
                        <div class="ml-auto">
                            <i class="fas fa-check-circle fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger">
                <div class="card-body">
                    <div class="d-flex">
                        <div>
                            <h3 class="text-white">3</h3>
                            <p class="text-white">严重事件</p>
                        </div>
                        <div class="ml-auto">
                            <i class="fas fa-fire fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 事件详情模态框 -->
<div class="modal fade" id="eventDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">事件详情</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body" id="eventDetailsBody">
                <!-- 事件详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" onclick="markCurrentEventAsHandled()">标记为已处理</button>
            </div>
        </div>
    </div>
</div>

<script>
// 刷新事件列表
function refreshEvents() {
    location.reload();
}

// 查看事件详情
function viewEventDetails(eventId) {
    // 这里可以通过API获取事件详情
    const detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <h5>基本信息</h5>
                <table class="table table-sm">
                    <tr><td><strong>事件ID:</strong></td><td>${eventId}</td></tr>
                    <tr><td><strong>时间:</strong></td><td>2025-07-12 13:45:23</td></tr>
                    <tr><td><strong>类型:</strong></td><td>目标检测</td></tr>
                    <tr><td><strong>摄像头:</strong></td><td>前门摄像头</td></tr>
                    <tr><td><strong>置信度:</strong></td><td>0.85</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h5>检测结果</h5>
                <p>检测到1个人员目标，位置坐标: (120, 200, 300, 450)</p>
                <p>检测算法: YOLOv8</p>
                <p>处理时间: 45ms</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h5>截图</h5>
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7kuovku7blm77niYc8L3RleHQ+PC9zdmc+" 
                     class="img-fluid" alt="事件截图" style="max-height: 300px;">
            </div>
        </div>
    `;
    
    document.getElementById('eventDetailsBody').innerHTML = detailsHtml;
    $('#eventDetailsModal').modal('show');
}

// 标记为已处理
function markAsHandled(eventId) {
    if (confirm('确定要标记此事件为已处理吗？')) {
        // 这里可以通过API更新事件状态
        alert(`事件 ${eventId} 已标记为已处理`);
        refreshEvents();
    }
}

// 删除事件
function deleteEvent(eventId) {
    if (confirm('确定要删除此事件吗？此操作不可撤销。')) {
        // 这里可以通过API删除事件
        alert(`事件 ${eventId} 已删除`);
        refreshEvents();
    }
}

// 清空所有事件
function clearAllEvents() {
    if (confirm('确定要清空所有事件吗？此操作不可撤销。')) {
        // 这里可以通过API清空所有事件
        alert('所有事件已清空');
        refreshEvents();
    }
}

// 应用筛选
function applyFilters() {
    const eventType = document.getElementById('eventTypeFilter').value;
    const severity = document.getElementById('severityFilter').value;
    const date = document.getElementById('dateFilter').value;
    
    // 这里可以根据筛选条件重新加载数据
    console.log('应用筛选:', { eventType, severity, date });
}

// 页面加载时设置今天的日期
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('dateFilter').value = today;
});
</script>
{% endblock %}
