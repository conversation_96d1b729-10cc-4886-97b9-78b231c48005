"""
Cerberus Central Platform - 中央管理平台主程序
提供统一的Web仪表盘、API服务和数据聚合功能
"""

import logging
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import HTMLResponse

from app.core.config import get_settings
from app.core.database import create_tables
from app.core.mqtt_subscriber import MQTTSubscriber
from app.core.redis_client import get_redis_client
from app.api.v1.api import api_router
from app.services.node_service import NodeService
from app.services.event_service import EventService
from app.services.analytics_service import AnalyticsService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 全局变量
mqtt_subscriber = None
background_tasks = []

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("Starting Cerberus Central Platform...")
    
    # 创建数据库表
    await create_tables()
    
    # 初始化Redis连接
    redis_client = await get_redis_client()
    
    # 启动MQTT订阅器
    global mqtt_subscriber
    settings = get_settings()
    mqtt_subscriber = MQTTSubscriber(settings)
    await mqtt_subscriber.start()
    
    # 启动后台任务
    await start_background_tasks()
    
    logger.info("Central Platform started successfully")
    
    yield
    
    # 关闭时执行
    logger.info("Shutting down Central Platform...")
    
    # 停止MQTT订阅器
    if mqtt_subscriber:
        await mqtt_subscriber.stop()
    
    # 停止后台任务
    await stop_background_tasks()
    
    # 关闭Redis连接
    if redis_client:
        await redis_client.close()
    
    logger.info("Central Platform shutdown complete")

async def start_background_tasks():
    """启动后台任务"""
    global background_tasks
    
    # 数据清理任务
    cleanup_task = asyncio.create_task(data_cleanup_task())
    background_tasks.append(cleanup_task)
    
    # 统计分析任务
    analytics_task = asyncio.create_task(analytics_task_runner())
    background_tasks.append(analytics_task)
    
    logger.info(f"Started {len(background_tasks)} background tasks")

async def stop_background_tasks():
    """停止后台任务"""
    global background_tasks
    
    for task in background_tasks:
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass
    
    background_tasks.clear()
    logger.info("All background tasks stopped")

async def data_cleanup_task():
    """数据清理任务"""
    while True:
        try:
            # 清理过期的事件数据
            event_service = EventService()
            await event_service.cleanup_expired_events()
            
            # 等待1小时
            await asyncio.sleep(3600)
            
        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error(f"Error in data cleanup task: {e}")
            await asyncio.sleep(300)  # 出错时等待5分钟

async def analytics_task_runner():
    """统计分析任务"""
    while True:
        try:
            # 生成统计报告
            analytics_service = AnalyticsService()
            await analytics_service.generate_daily_report()
            
            # 等待24小时
            await asyncio.sleep(86400)
            
        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error(f"Error in analytics task: {e}")
            await asyncio.sleep(3600)  # 出错时等待1小时

# 创建FastAPI应用
app = FastAPI(
    title="Cerberus Central Platform",
    description="分布式智能安防监控平台 - 中央管理平台",
    version="2.0.0",
    lifespan=lifespan
)

# 获取配置
settings = get_settings()

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# 模板引擎
templates = Jinja2Templates(directory="app/templates")

# 包含API路由
app.include_router(api_router, prefix="/api/v1")

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """主仪表盘页面"""
    try:
        # 获取基本统计数据
        node_service = NodeService()
        event_service = EventService()
        
        stats = {
            'total_nodes': await node_service.get_total_count(),
            'online_nodes': await node_service.get_online_count(),
            'total_cameras': await node_service.get_total_cameras(),
            'total_events_today': await event_service.get_today_count(),
            'recent_events': await event_service.get_recent_events(limit=10)
        }
        
        return templates.TemplateResponse(
            "dashboard.html",
            {"request": request, "stats": stats}
        )
    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/nodes", response_class=HTMLResponse)
async def nodes_page(request: Request):
    """节点管理页面"""
    return templates.TemplateResponse("nodes.html", {"request": request})

@app.get("/events", response_class=HTMLResponse)
async def events_page(request: Request):
    """事件管理页面"""
    return templates.TemplateResponse("events.html", {"request": request})

@app.get("/analytics", response_class=HTMLResponse)
async def analytics_page(request: Request):
    """统计分析页面"""
    return templates.TemplateResponse("analytics.html", {"request": request})

@app.get("/settings", response_class=HTMLResponse)
async def settings_page(request: Request):
    """系统设置页面"""
    return templates.TemplateResponse("settings.html", {"request": request})

@app.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        # 检查数据库连接
        from app.core.database import get_db
        from sqlalchemy import text
        async for db in get_db():
            await db.execute(text("SELECT 1"))
            break
        
        # 检查Redis连接
        redis_client = await get_redis_client()
        await redis_client.ping()
        
        # 检查MQTT连接
        mqtt_status = mqtt_subscriber.is_connected() if mqtt_subscriber else False
        
        return {
            "status": "healthy",
            "database": "connected",
            "redis": "connected",
            "mqtt": "connected" if mqtt_status else "disconnected",
            "background_tasks": len(background_tasks)
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")

@app.get("/metrics")
async def get_metrics():
    """获取系统指标"""
    try:
        node_service = NodeService()
        event_service = EventService()
        
        metrics = {
            "nodes": {
                "total": await node_service.get_total_count(),
                "online": await node_service.get_online_count(),
                "offline": await node_service.get_offline_count()
            },
            "cameras": {
                "total": await node_service.get_total_cameras(),
                "active": await node_service.get_active_cameras()
            },
            "events": {
                "today": await event_service.get_today_count(),
                "this_week": await event_service.get_week_count(),
                "this_month": await event_service.get_month_count()
            },
            "system": {
                "uptime": await get_system_uptime(),
                "version": "2.0.0"
            }
        }
        
        return metrics
    except Exception as e:
        logger.error(f"Error getting metrics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

async def get_system_uptime():
    """获取系统运行时间"""
    import time
    import psutil
    
    boot_time = psutil.boot_time()
    current_time = time.time()
    uptime_seconds = current_time - boot_time
    
    return {
        "seconds": int(uptime_seconds),
        "formatted": format_uptime(uptime_seconds)
    }

def format_uptime(seconds):
    """格式化运行时间"""
    days = int(seconds // 86400)
    hours = int((seconds % 86400) // 3600)
    minutes = int((seconds % 3600) // 60)
    
    if days > 0:
        return f"{days}天 {hours}小时 {minutes}分钟"
    elif hours > 0:
        return f"{hours}小时 {minutes}分钟"
    else:
        return f"{minutes}分钟"

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
