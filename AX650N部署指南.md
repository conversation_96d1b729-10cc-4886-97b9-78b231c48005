# 🚀 AX650N边缘硬件部署指南

## 🔧 AX650N硬件概述

### 📊 **AX650N技术规格**
```
芯片型号: 爱芯元智 AX650N
AI算力: 43.2 TOPS@INT4 / 10.8 TOPS@INT8
CPU: 四核ARM Cortex-A78 @ 2.0GHz
GPU: Mali-G57 MC4
内存: 支持LPDDR4X-4266，最大32GB
视频: 4K@60fps H.264/H.265编解码
接口: PCIe 3.0、USB 3.0、MIPI CSI、以太网
功耗: 典型功耗 < 15W
```

### 🏭 **适用场景**
- 智能安防监控
- 工业质检
- 自动驾驶
- 机器人视觉
- 边缘AI计算

## 🛠️ **硬件准备**

### 📦 **推荐硬件配置**
```
主板: AX650N开发板/工控机
存储: 256GB+ eUFS/SSD
网络: 千兆以太网
电源: 12V/3A适配器
散热: 主动散热风扇
外壳: 工业级防护外壳
```

### 🌐 **网络设备**
```
交换机: 24口千兆PoE交换机
路由器: 企业级路由器
摄像头: 支持RTSP的IP摄像头
网线: Cat6网线
```

## 🚀 **快速部署**

### 1️⃣ **一键部署脚本**
```bash
# 下载部署脚本
wget https://raw.githubusercontent.com/your-repo/CameraDetection/main/deploy_ax650n.sh

# 给脚本执行权限
chmod +x deploy_ax650n.sh

# 运行部署脚本
./deploy_ax650n.sh
```

### 2️⃣ **手动部署步骤**

#### **系统环境准备**
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础依赖
sudo apt install -y curl wget git python3 python3-pip build-essential cmake

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### **AX650N SDK安装**
```bash
# 下载AX650N SDK (从爱芯元智官网)
wget https://www.axera-tech.com/downloads/ax650n-sdk-v1.0.tar.gz

# 解压SDK
sudo tar -xzf ax650n-sdk-v1.0.tar.gz -C /opt/

# 设置环境变量
echo 'export AX_NPU_SDK_PATH=/opt/ax650n-sdk' >> ~/.bashrc
echo 'export LD_LIBRARY_PATH=$AX_NPU_SDK_PATH/lib:$LD_LIBRARY_PATH' >> ~/.bashrc
echo 'export PATH=$AX_NPU_SDK_PATH/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 安装NPU驱动
cd /opt/ax650n-sdk
sudo ./install_driver.sh
```

#### **项目部署**
```bash
# 克隆项目
git clone https://github.com/your-repo/CameraDetection.git
cd CameraDetection

# 使用AX650N配置启动
docker-compose -f docker-compose.ax650n.yml up -d
```

## 🎯 **配置优化**

### 📹 **摄像头配置**
```json
{
  "camera_1": {
    "name": "AX650N摄像头1",
    "rtsp_url": "rtsp://admin:password@192.168.1.101:554/stream1",
    "resolution": "1920x1080",
    "fps": 25,
    "npu_acceleration": true,
    "detectors": ["object_detection", "face_recognition"],
    "performance": {
      "max_concurrent_streams": 4,
      "npu_cores": 2,
      "inference_batch_size": 1
    }
  }
}
```

### 🤖 **AI模型优化**
```yaml
# config/ai_config.yaml
ax650n_config:
  npu_enabled: true
  npu_cores: 4
  model_format: "axmodel"
  quantization: "int8"
  
detectors:
  object_detection:
    model_path: "/app/models/ax650n/yolov8n.axmodel"
    confidence_threshold: 0.5
    nms_threshold: 0.4
    input_size: [640, 640]
    
  face_recognition:
    model_path: "/app/models/ax650n/retinaface.axmodel"
    confidence_threshold: 0.7
    npu_cores: 2
```

### ⚡ **性能调优**
```bash
# NPU性能模式
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# 内存优化
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# NPU频率设置
echo 800000000 | sudo tee /sys/class/devfreq/*/max_freq
```

## 📊 **性能监控**

### 🔍 **系统监控**
```bash
# NPU使用率
cat /sys/class/npu/npu0/utilization

# 温度监控
cat /sys/class/thermal/thermal_zone*/temp

# 内存使用
free -h

# CPU使用率
htop
```

### 📈 **API监控**
```bash
# 检查服务状态
curl http://localhost:5000/status

# 性能统计
curl http://localhost:5000/metrics

# NPU状态
curl http://localhost:5000/npu/status
```

## 🔧 **故障排除**

### ❌ **常见问题**

#### **NPU设备不可用**
```bash
# 检查NPU设备
ls -la /dev/ax_npu*

# 检查驱动
lsmod | grep ax_npu

# 重新安装驱动
sudo /opt/ax650n-sdk/install_driver.sh
```

#### **模型加载失败**
```bash
# 检查模型文件
ls -la models/ax650n/

# 验证模型格式
file models/ax650n/yolov8n.axmodel

# 转换模型
ax_converter --input yolov8n.onnx --output yolov8n.axmodel --target AX650N
```

#### **性能不佳**
```bash
# 检查NPU利用率
watch -n 1 cat /sys/class/npu/npu0/utilization

# 调整NPU核心数
export AX_NPU_CORES=4

# 优化推理批次
export INFERENCE_BATCH_SIZE=2
```

## 📱 **远程管理**

### 🌐 **Web管理界面**
```bash
# 本地访问
http://*************:5000

# 远程访问 (配置端口转发)
ssh -L 5000:localhost:5000 user@*************
```

### 📡 **MQTT连接**
```bash
# 配置MQTT连接到中央平台
export MQTT_BROKER_HOST="central.company.com"
export MQTT_BROKER_PORT=1883
export MQTT_USERNAME="edge_ax650n"
export MQTT_PASSWORD="your_password"

# 重启服务
docker-compose restart
```

## 🔒 **安全配置**

### 🛡️ **网络安全**
```bash
# 配置防火墙
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 5000/tcp  # API
sudo ufw deny 1883/tcp   # 仅内网MQTT

# 配置SSH密钥认证
ssh-keygen -t rsa -b 4096
ssh-copy-id user@edge-device
```

### 🔐 **数据加密**
```yaml
# 配置TLS加密
security:
  tls_enabled: true
  cert_file: "/app/certs/server.crt"
  key_file: "/app/certs/server.key"
  
mqtt:
  use_tls: true
  ca_cert: "/app/certs/ca.crt"
```

## 📈 **扩展部署**

### 🏭 **多节点部署**
```yaml
# docker-compose.multi-ax650n.yml
version: '3.8'
services:
  edge-node-1:
    extends:
      file: docker-compose.ax650n.yml
      service: edge-node-ax650n
    environment:
      NODE_ID: "ax650n-node-1"
      
  edge-node-2:
    extends:
      file: docker-compose.ax650n.yml
      service: edge-node-ax650n
    environment:
      NODE_ID: "ax650n-node-2"
    ports:
      - "5001:5000"
```

### 🌐 **集群管理**
```bash
# 使用Docker Swarm
docker swarm init
docker stack deploy -c docker-compose.ax650n.yml cerberus

# 或使用Kubernetes
kubectl apply -f k8s/ax650n-deployment.yaml
```

## 🎯 **最佳实践**

### ⚡ **性能优化**
1. **模型量化** - 使用INT8量化提升推理速度
2. **批处理** - 合理设置批次大小
3. **多核并行** - 充分利用NPU多核心
4. **内存管理** - 优化内存分配和回收

### 🔧 **运维管理**
1. **监控告警** - 设置性能和错误监控
2. **自动更新** - 配置自动更新机制
3. **备份策略** - 定期备份配置和数据
4. **日志管理** - 配置日志轮转和清理

这样，您就可以在AX650N硬件上成功部署高性能的边缘AI监控系统！🚀
