# 🏭 实际部署：边缘计算硬件连接方案

## 🌐 网络架构设计

### 📊 **典型企业部署架构**
```
                    ☁️ 云端/总部
                 [中央管理平台]
                      ↕️ 
                  🌐 互联网/VPN
                      ↕️
    ┌─────────────────┼─────────────────┐
    │                 │                 │
🏢 分公司A          🏢 分公司B          🏢 分公司C
[边缘节点A]        [边缘节点B]        [边缘节点C]
    ↕️                 ↕️                 ↕️
🏠 局域网           🏠 局域网           🏠 局域网
📹📹📹            📹📹📹            📹📹📹
摄像头群           摄像头群           摄像头群
```

### 🔧 **单个现场部署架构**
```
🏢 现场机房/弱电间
┌─────────────────────────────────┐
│  🖥️ 边缘计算设备                │
│  ├─ Docker容器运行环境          │
│  ├─ Cerberus边缘节点           │
│  ├─ AI检测引擎                 │
│  └─ 本地存储                   │
└─────────────────────────────────┘
         ↕️ 千兆以太网
┌─────────────────────────────────┐
│  🌐 网络交换机                  │
│  ├─ PoE供电端口                │
│  ├─ 上联路由器                 │
│  └─ 连接摄像头                 │
└─────────────────────────────────┘
    ↕️     ↕️     ↕️     ↕️
   📹    📹    📹    📹
  摄像头1 摄像头2 摄像头3 摄像头4
```

## 🔌 **硬件连接步骤**

### 1. **边缘设备安装**
```bash
# 选择合适位置
- 机房/弱电间（温度控制）
- 靠近摄像头网络（减少延迟）
- 有稳定电源和网络
- 便于维护访问

# 安装操作系统
- Ubuntu 20.04 LTS (推荐)
- CentOS 8
- Windows 10/11 (支持Docker Desktop)
```

### 2. **网络配置**
```bash
# 配置静态IP
sudo nano /etc/netplan/01-netcfg.yaml

network:
  version: 2
  ethernets:
    eth0:
      dhcp4: false
      addresses: [*************/24]
      gateway4: ***********
      nameservers:
        addresses: [*******, *******]

# 应用配置
sudo netplan apply
```

### 3. **Docker环境部署**
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl enable docker
sudo systemctl start docker
```

## 📹 **摄像头连接配置**

### 🔧 **IP摄像头配置**
```json
{
  "camera_1": {
    "name": "前门摄像头",
    "rtsp_url": "rtsp://admin:password@***********01:554/stream1",
    "ip_address": "***********01",
    "brand": "海康威视",
    "model": "DS-2CD2T47G1-L",
    "resolution": "1920x1080",
    "fps": 25,
    "enabled": true,
    "location": "主入口",
    "detectors": ["object_detection", "face_recognition"]
  },
  "camera_2": {
    "name": "停车场摄像头", 
    "rtsp_url": "rtsp://admin:password@***********02:554/stream1",
    "ip_address": "***********02",
    "brand": "大华",
    "model": "DH-IPC-HFW4433M-I2",
    "resolution": "1920x1080",
    "fps": 25,
    "enabled": true,
    "location": "停车场",
    "detectors": ["object_detection", "line_crossing"]
  }
}
```

### 🌐 **网络规划**
```
网段规划：
- 边缘设备: *************
- 摄像头群: ***********01-120
- 网关路由: ***********
- DNS服务: *******

端口规划：
- 边缘节点API: 5000
- RTSP流: 554
- HTTP管理: 80
- HTTPS: 443
```

## 🚀 **部署实施步骤**

### 第一步：硬件准备
```bash
# 1. 采购边缘计算设备
Intel NUC11PAHi7 (推荐)
- CPU: Intel i7-1165G7
- 内存: 32GB DDR4
- 存储: 1TB NVMe SSD
- 网络: 千兆以太网

# 2. 网络设备
- 24口千兆PoE交换机
- 企业级路由器
- 网线、光纤等
```

### 第二步：系统部署
```bash
# 1. 克隆项目到边缘设备
git clone https://github.com/your-repo/CameraDetection.git
cd CameraDetection

# 2. 配置环境变量
cp .env.example .env
nano .env

# 设置边缘节点配置
NODE_ID=edge-node-site-A
MQTT_BROKER_HOST=central.company.com
CENTRAL_PLATFORM_URL=https://monitor.company.com

# 3. 启动边缘节点
docker-compose up -d edge-node-1
```

### 第三步：摄像头配置
```bash
# 1. 编辑摄像头配置
nano edge-node/config/cameras.json

# 2. 配置真实RTSP地址
{
  "camera_1": {
    "rtsp_url": "rtsp://admin:HikVision123@***********01:554/Streaming/Channels/101"
  }
}

# 3. 重启应用配置
docker-compose restart edge-node-1
```

## 🔗 **连接中央平台**

### 🌐 **云端连接方式**

#### 1. **VPN连接**
```bash
# 安装OpenVPN客户端
sudo apt install openvpn

# 配置VPN连接
sudo openvpn --config company-vpn.ovpn

# 设置开机自启
sudo systemctl enable openvpn@company-vpn
```

#### 2. **公网IP + 防火墙**
```bash
# 配置防火墙规则
sudo ufw allow 5000/tcp  # 边缘节点API
sudo ufw allow 1883/tcp  # MQTT
sudo ufw allow 22/tcp    # SSH管理
sudo ufw enable

# 配置端口转发（路由器）
外网端口 -> 内网IP:端口
8080 -> *************:5000
```

#### 3. **内网穿透**
```bash
# 使用frp内网穿透
# 服务端（云端）
./frps -c frps.ini

# 客户端（边缘端）
./frpc -c frpc.ini

[common]
server_addr = your-server.com
server_port = 7000

[edge-api]
type = tcp
local_ip = 127.0.0.1
local_port = 5000
remote_port = 6000
```

## 📊 **监控和维护**

### 🔧 **远程管理**
```bash
# SSH远程登录
ssh admin@*************

# 查看系统状态
docker-compose ps
docker-compose logs edge-node-1

# 更新系统
git pull
docker-compose pull
docker-compose up -d
```

### 📈 **性能监控**
```bash
# 系统资源监控
htop
nvidia-smi  # GPU使用率
df -h       # 磁盘使用

# 应用监控
curl http://localhost:5000/status
curl http://localhost:5000/metrics
```

## 💡 **最佳实践**

### 🔒 **安全配置**
```bash
# 1. 修改默认密码
sudo passwd admin

# 2. 配置SSH密钥认证
ssh-keygen -t rsa -b 4096
ssh-copy-id admin@edge-device

# 3. 禁用密码登录
sudo nano /etc/ssh/sshd_config
PasswordAuthentication no

# 4. 配置自动更新
sudo apt install unattended-upgrades
sudo dpkg-reconfigure unattended-upgrades
```

### 📦 **备份策略**
```bash
# 配置文件备份
tar -czf config-backup-$(date +%Y%m%d).tar.gz edge-node/config/

# 数据库备份
docker exec postgres pg_dump -U cerberus cerberus > backup.sql

# 自动备份脚本
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /backup/edge-config-$DATE.tar.gz /app/edge-node/config/
find /backup -name "edge-config-*.tar.gz" -mtime +7 -delete
```

## 🎯 **部署检查清单**

### ✅ **硬件检查**
- [ ] 边缘计算设备正常启动
- [ ] 网络连接稳定
- [ ] 摄像头可以ping通
- [ ] 存储空间充足

### ✅ **软件检查**
- [ ] Docker服务运行正常
- [ ] 边缘节点容器启动
- [ ] MQTT连接成功
- [ ] API接口响应正常

### ✅ **功能检查**
- [ ] 摄像头视频流正常
- [ ] AI检测功能工作
- [ ] 事件上报成功
- [ ] Web界面可以管理

这样，您就可以在实际环境中部署边缘计算硬件，连接真实的摄像头，实现完整的智能监控系统！
