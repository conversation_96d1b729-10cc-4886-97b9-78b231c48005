# Cerberus智能监控系统 - API使用示例

## 🚀 边缘节点API (http://localhost:5000)

### 1. 首页 - 查看API文档
```bash
curl http://localhost:5000/
```
**响应示例:**
```json
{
  "service": "Cerberus Edge Node",
  "version": "1.0.0", 
  "node_id": "edge-node-1",
  "status": "running",
  "api_endpoints": {
    "health": "/health",
    "status": "/status",
    "cameras": "/cameras", 
    "config": "/config",
    "start_camera": "/cameras/{camera_id}/start",
    "stop_camera": "/cameras/{camera_id}/stop"
  }
}
```

### 2. 健康检查
```bash
curl http://localhost:5000/health
```

### 3. 节点状态
```bash
curl http://localhost:5000/status
```

### 4. 摄像头列表
```bash
curl http://localhost:5000/cameras
```
**响应示例:**
```json
{
  "camera_1": {
    "camera_id": "camera_1",
    "name": "前门摄像头",
    "status": "inactive",
    "is_active": false,
    "fps": 0.0,
    "frame_count": 0,
    "total_detections": 0,
    "error_count": 0,
    "recording_status": "idle"
  },
  "camera_2": {
    "camera_id": "camera_2", 
    "name": "后门摄像头",
    "status": "inactive",
    "is_active": false,
    "fps": 0.0,
    "frame_count": 0,
    "total_detections": 0,
    "error_count": 0,
    "recording_status": "idle"
  }
}
```

### 5. 启动摄像头
```bash
curl -X POST http://localhost:5000/cameras/camera_1/start
```

### 6. 停止摄像头
```bash
curl -X POST http://localhost:5000/cameras/camera_1/stop
```

### 7. 获取配置
```bash
curl http://localhost:5000/config
```

## 🌐 中央平台 (http://localhost:9997)

### 健康检查
```bash
curl http://localhost:9997/health
```
**响应示例:**
```json
{
  "status": "healthy",
  "database": "connected",
  "redis": "connected", 
  "mqtt": "connected",
  "background_tasks": 2
}
```

## 📊 MinIO存储管理 (http://localhost:9000)
- **用户名**: cerberus
- **密码**: cerberus123

## 📡 MQTT管理界面 (http://localhost:18083)
- **用户名**: admin
- **密码**: public

## 🎥 配置摄像头

### 编辑摄像头配置文件
文件位置: `edge-node/config/cameras.json`

```json
{
  "camera_1": {
    "name": "前门摄像头",
    "rtsp_url": "rtsp://admin:password@*************:554/stream1",
    "enabled": true,
    "fps": 25,
    "resolution": "1280x720",
    "detection_zones": [
      {
        "name": "entrance",
        "polygon": [[100, 100], [500, 100], [500, 400], [100, 400]],
        "description": "入口检测区域"
      }
    ],
    "detectors": ["object_detection", "face_recognition"],
    "auto_reconnect": true,
    "reconnect_interval": 30
  }
}
```

### 重启边缘节点应用配置
```bash
docker-compose restart edge-node-1
```

## 🤖 AI检测功能测试

### 1. 启动摄像头检测
```bash
# 启动camera_1
curl -X POST http://localhost:5000/cameras/camera_1/start

# 查看状态
curl http://localhost:5000/status
```

### 2. 查看检测统计
```bash
curl http://localhost:5000/status | jq '.detection_stats'
```

## 🔧 常用操作

### 查看所有容器状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 边缘节点日志
docker-compose logs edge-node-1 --tail=20

# 中央平台日志  
docker-compose logs central-platform --tail=20

# 所有服务日志
docker-compose logs --tail=20
```

### 重启服务
```bash
# 重启边缘节点
docker-compose restart edge-node-1

# 重启中央平台
docker-compose restart central-platform

# 重启所有服务
docker-compose restart
```

## 📱 Web界面访问

1. **中央平台管理界面**: http://localhost:9997
   - 系统仪表板
   - 摄像头管理
   - 事件告警
   - 系统设置

2. **MinIO文件管理**: http://localhost:9000
   - 查看存储的视频文件
   - 管理存储桶

3. **MQTT管理界面**: http://localhost:18083
   - 查看消息队列状态
   - 监控消息流量

## 🚨 故障排除

### 如果API无法访问:
1. 检查容器状态: `docker-compose ps`
2. 查看日志: `docker-compose logs [service-name]`
3. 重启服务: `docker-compose restart [service-name]`

### 如果摄像头无法连接:
1. 检查RTSP地址是否正确
2. 确认网络连接
3. 查看边缘节点日志中的错误信息

### 如果检测不工作:
1. 确认摄像头已启动
2. 检查检测器配置
3. 查看检测统计信息
