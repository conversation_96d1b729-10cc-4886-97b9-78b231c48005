{% extends "base.html" %}

{% block title %}统计分析 - Cerberus监控系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 时间范围选择 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label>开始日期</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-3">
                            <label>结束日期</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                        <div class="col-md-3">
                            <label>摄像头</label>
                            <select class="form-control" id="cameraFilter">
                                <option value="">所有摄像头</option>
                                <option value="camera_1">前门摄像头</option>
                                <option value="camera_2">后门摄像头</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label>&nbsp;</label>
                            <button class="btn btn-primary form-control" onclick="updateCharts()">
                                <i class="fas fa-chart-bar"></i>
                                更新图表
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary">
                <div class="card-body">
                    <div class="d-flex">
                        <div>
                            <h3 class="text-white" id="totalEvents">156</h3>
                            <p class="text-white">总事件数</p>
                        </div>
                        <div class="ml-auto">
                            <i class="fas fa-list fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info">
                <div class="card-body">
                    <div class="d-flex">
                        <div>
                            <h3 class="text-white" id="avgPerDay">12.3</h3>
                            <p class="text-white">日均事件</p>
                        </div>
                        <div class="ml-auto">
                            <i class="fas fa-calendar-day fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success">
                <div class="card-body">
                    <div class="d-flex">
                        <div>
                            <h3 class="text-white" id="detectionRate">87.5%</h3>
                            <p class="text-white">检测准确率</p>
                        </div>
                        <div class="ml-auto">
                            <i class="fas fa-bullseye fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning">
                <div class="card-body">
                    <div class="d-flex">
                        <div>
                            <h3 class="text-white" id="avgResponseTime">2.3s</h3>
                            <p class="text-white">平均响应时间</p>
                        </div>
                        <div class="ml-auto">
                            <i class="fas fa-clock fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>事件趋势图</h4>
                </div>
                <div class="card-body">
                    <canvas id="eventTrendChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>事件类型分布</h4>
                </div>
                <div class="card-body">
                    <canvas id="eventTypeChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>摄像头活跃度</h4>
                </div>
                <div class="card-body">
                    <canvas id="cameraActivityChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>检测算法性能</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>算法</th>
                                    <th>检测次数</th>
                                    <th>准确率</th>
                                    <th>平均耗时</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>目标检测</td>
                                    <td>1,234</td>
                                    <td>89.2%</td>
                                    <td>45ms</td>
                                </tr>
                                <tr>
                                    <td>人脸识别</td>
                                    <td>567</td>
                                    <td>92.1%</td>
                                    <td>78ms</td>
                                </tr>
                                <tr>
                                    <td>越线检测</td>
                                    <td>89</td>
                                    <td>95.5%</td>
                                    <td>23ms</td>
                                </tr>
                                <tr>
                                    <td>摄像头异常</td>
                                    <td>12</td>
                                    <td>100%</td>
                                    <td>5ms</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 热力图 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4>24小时事件热力图</h4>
                </div>
                <div class="card-body">
                    <canvas id="heatmapChart" width="800" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 初始化图表
let eventTrendChart, eventTypeChart, cameraActivityChart, heatmapChart;

document.addEventListener('DOMContentLoaded', function() {
    // 设置默认日期范围（最近7天）
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 7);
    
    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
    
    // 初始化图表
    initCharts();
});

function initCharts() {
    // 事件趋势图
    const trendCtx = document.getElementById('eventTrendChart').getContext('2d');
    eventTrendChart = new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: ['07-06', '07-07', '07-08', '07-09', '07-10', '07-11', '07-12'],
            datasets: [{
                label: '事件数量',
                data: [12, 19, 8, 15, 22, 18, 24],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // 事件类型分布饼图
    const typeCtx = document.getElementById('eventTypeChart').getContext('2d');
    eventTypeChart = new Chart(typeCtx, {
        type: 'doughnut',
        data: {
            labels: ['目标检测', '人脸识别', '越线检测', '摄像头异常'],
            datasets: [{
                data: [45, 25, 20, 10],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // 摄像头活跃度柱状图
    const activityCtx = document.getElementById('cameraActivityChart').getContext('2d');
    cameraActivityChart = new Chart(activityCtx, {
        type: 'bar',
        data: {
            labels: ['前门摄像头', '后门摄像头'],
            datasets: [{
                label: '事件数量',
                data: [89, 67],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // 24小时热力图
    const heatmapCtx = document.getElementById('heatmapChart').getContext('2d');
    const heatmapData = [];
    for (let hour = 0; hour < 24; hour++) {
        heatmapData.push({
            x: hour,
            y: 0,
            v: Math.floor(Math.random() * 20)
        });
    }

    heatmapChart = new Chart(heatmapCtx, {
        type: 'scatter',
        data: {
            datasets: [{
                label: '事件密度',
                data: heatmapData,
                backgroundColor: function(context) {
                    const value = context.parsed.v;
                    const alpha = value / 20;
                    return `rgba(255, 99, 132, ${alpha})`;
                },
                pointRadius: function(context) {
                    return Math.max(context.parsed.v / 2, 3);
                }
            }]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    type: 'linear',
                    position: 'bottom',
                    min: 0,
                    max: 23,
                    title: {
                        display: true,
                        text: '小时'
                    }
                },
                y: {
                    display: false
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

function updateCharts() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const camera = document.getElementById('cameraFilter').value;
    
    // 这里可以根据选择的参数重新获取数据并更新图表
    console.log('更新图表:', { startDate, endDate, camera });
    
    // 模拟数据更新
    const newData = Array.from({length: 7}, () => Math.floor(Math.random() * 30));
    eventTrendChart.data.datasets[0].data = newData;
    eventTrendChart.update();
    
    alert('图表已更新');
}

// 导出报告
function exportReport() {
    alert('报告导出功能开发中...');
}
</script>
{% endblock %}
