# Cerberus智能监控系统使用指南

## 🚀 快速开始

### 1. 系统访问地址
- **中央平台**: http://localhost:9997
- **MinIO存储**: http://localhost:9000 (用户名: cerberus, 密码: cerberus123)
- **MQTT管理**: http://localhost:18083 (用户名: admin, 密码: public)
- **边缘节点API**: http://localhost:5000

### 2. 🎥 配置摄像头

#### 修改摄像头配置
编辑文件: `edge-node/config/cameras.json`

```json
{
  "camera_1": {
    "name": "前门摄像头",
    "rtsp_url": "rtsp://用户名:密码@摄像头IP:554/stream1",
    "enabled": true,
    "fps": 25,
    "resolution": "1280x720",
    "detection_zones": [
      {
        "name": "entrance",
        "polygon": [[100, 100], [500, 100], [500, 400], [100, 400]],
        "description": "入口检测区域"
      }
    ],
    "detectors": ["object_detection", "face_recognition"],
    "auto_reconnect": true,
    "reconnect_interval": 30
  }
}
```

#### 支持的摄像头格式
- RTSP流: `rtsp://用户名:密码@IP:端口/路径`
- HTTP流: `http://IP:端口/路径`
- 本地摄像头: `/dev/video0` (Linux) 或 `0` (Windows)
- 视频文件: `/path/to/video.mp4`

### 3. 🤖 AI检测功能

#### 可用的检测器
1. **目标检测** (object_detection)
   - 检测人员、车辆、自行车等
   - 基于YOLOv8模型

2. **人脸识别** (face_recognition)
   - 人脸检测和身份识别
   - 陌生人告警

3. **越线检测** (line_crossing)
   - 检测物体穿越预设线条
   - 支持双向检测

4. **摄像头异常检测** (camera_obstruction)
   - 检测摄像头遮挡
   - 检测画面冻结

### 4. 📊 使用流程

#### 步骤1: 配置摄像头
1. 修改 `edge-node/config/cameras.json`
2. 设置正确的RTSP地址
3. 启用需要的检测器

#### 步骤2: 重启边缘节点
```bash
docker-compose restart edge-node-1
```

#### 步骤3: 查看实时监控
- 访问中央平台: http://localhost:9997
- 查看实时视频流和检测结果

#### 步骤4: 配置告警
- 在中央平台设置告警规则
- 配置通知方式(邮件、短信等)

### 5. 🔧 高级配置

#### 调整检测参数
编辑 `edge-node/config/detectors.json`:
- `confidence_threshold`: 检测置信度阈值
- `target_classes`: 目标检测类别
- `sensitivity`: 检测灵敏度

#### 存储配置
- 视频文件存储在MinIO中
- 访问MinIO管理界面查看存储的视频

### 6. 🚨 常见问题

#### Q: 摄像头连接失败？
A: 检查RTSP地址、用户名密码是否正确

#### Q: 检测效果不好？
A: 调整confidence_threshold参数，降低阈值提高检测率

#### Q: 系统运行缓慢？
A: 降低视频分辨率或FPS，减少检测器数量

### 7. 📱 API接口

#### 边缘节点API (http://localhost:5000)
- `GET /health` - 健康检查
- `GET /status` - 节点状态
- `GET /cameras` - 摄像头列表
- `POST /cameras/{id}/start` - 启动摄像头
- `POST /cameras/{id}/stop` - 停止摄像头

#### 中央平台API (http://localhost:9997)
- `GET /api/v1/nodes` - 节点列表
- `GET /api/v1/events` - 事件列表
- `GET /api/v1/cameras` - 摄像头管理

### 8. 🔄 系统维护

#### 查看日志
```bash
# 查看边缘节点日志
docker-compose logs edge-node-1

# 查看中央平台日志
docker-compose logs central-platform

# 查看所有服务日志
docker-compose logs
```

#### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart edge-node-1
```

#### 更新配置
修改配置文件后需要重启对应的服务才能生效。
