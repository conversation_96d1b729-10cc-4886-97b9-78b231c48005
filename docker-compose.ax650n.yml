# Cerberus 智能监控系统 - AX650N边缘部署配置
version: '3.8'

services:
  # AX650N边缘节点
  edge-node-ax650n:
    build:
      context: ./edge-node
      dockerfile: Dockerfile.ax650n
    container_name: cerberus-edge-ax650n
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      # 节点配置
      NODE_ID: edge-node-ax650n
      NODE_NAME: "AX650N边缘节点"
      NODE_LOCATION: "现场部署"
      
      # NPU配置
      AX_NPU_ENABLED: "true"
      AX_NPU_DEVICE: "/dev/ax_npu"
      AX_NPU_CORES: "4"
      
      # MQTT配置 (连接远程中央平台)
      MQTT_BROKER_HOST: "your-central-server.com"  # 替换为实际地址
      MQTT_BROKER_PORT: 1883
      MQTT_USERNAME: "edge_node"
      MQTT_PASSWORD: "your_password"
      MQTT_CLIENT_ID: "edge-ax650n-001"
      
      # 存储配置
      MINIO_ENDPOINT: "your-central-server.com:9000"  # 替换为实际地址
      MINIO_ACCESS_KEY: "cerberus"
      MINIO_SECRET_KEY: "cerberus123"
      MINIO_BUCKET: "cerberus-edge-ax650n"
      
      # 本地缓存配置
      LOCAL_CACHE_SIZE_GB: "50"
      VIDEO_RETENTION_DAYS: "7"
      EVENT_RETENTION_DAYS: "30"
      
      # AI配置
      AI_MODEL_PATH: "/app/models/ax650n"
      DETECTION_CONFIDENCE: "0.5"
      NMS_THRESHOLD: "0.4"
      MAX_DETECTIONS: "100"
      
      # 性能配置
      MAX_CONCURRENT_STREAMS: "8"
      FRAME_BUFFER_SIZE: "30"
      PROCESSING_THREADS: "4"
      
      # 日志配置
      LOG_LEVEL: "INFO"
      LOG_MAX_SIZE: "100MB"
      LOG_BACKUP_COUNT: "5"
      
    devices:
      # NPU设备映射
      - "/dev/ax_npu:/dev/ax_npu"
      # 视频设备映射
      - "/dev/video0:/dev/video0"
      - "/dev/video1:/dev/video1"
      
    volumes:
      # 应用代码
      - ./edge-node:/app
      # 本地缓存
      - ./data/cache:/app/cache
      # 配置文件
      - ./edge-node/config:/app/config
      # 日志文件
      - ./logs:/app/logs
      # AI模型
      - ./models/ax650n:/app/models/ax650n
      # NPU驱动
      - /opt/ax650n-sdk:/opt/ax650n-sdk:ro
      
    privileged: true  # NPU访问需要特权模式
    
    networks:
      - edge-network
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 本地Redis缓存 (可选)
  redis-edge:
    image: redis:7-alpine
    container_name: cerberus-redis-edge
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_edge_data:/data
    networks:
      - edge-network
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru

  # 本地MQTT代理 (可选，用于离线工作)
  mqtt-edge:
    image: emqx/emqx:5.1
    container_name: cerberus-mqtt-edge
    restart: unless-stopped
    ports:
      - "1883:1883"
      - "8083:8083"
      - "18083:18083"
    environment:
      EMQX_NAME: "edge-mqtt"
      EMQX_HOST: "0.0.0.0"
    volumes:
      - mqtt_edge_data:/opt/emqx/data
      - mqtt_edge_log:/opt/emqx/log
    networks:
      - edge-network

networks:
  edge-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis_edge_data:
  mqtt_edge_data:
  mqtt_edge_log:
