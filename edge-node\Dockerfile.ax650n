# Cerberus Edge Node for AX650N
FROM ubuntu:20.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /app

# 更换为国内镜像源
RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    gcc \
    g++ \
    cmake \
    pkg-config \
    libopencv-dev \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    ffmpeg \
    wget \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 安装AX650N NPU驱动和SDK
COPY ax650n-sdk/ /opt/ax650n-sdk/
RUN cd /opt/ax650n-sdk && \
    chmod +x install.sh && \
    ./install.sh

# 设置NPU环境变量
ENV AX_NPU_SDK_PATH=/opt/ax650n-sdk
ENV LD_LIBRARY_PATH=$AX_NPU_SDK_PATH/lib:$LD_LIBRARY_PATH
ENV PATH=$AX_NPU_SDK_PATH/bin:$PATH

# 创建Python软链接
RUN ln -s /usr/bin/python3 /usr/bin/python

# 复制requirements文件
COPY requirements_ax650n.txt requirements.txt

# 安装Python依赖
RUN pip3 install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

# 安装AX650N Python SDK
RUN pip3 install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple ax-npu-python

# 复制应用代码
COPY . .

# 复制AX650N优化的AI模型
COPY models/ax650n/ models/

# 创建必要的目录
RUN mkdir -p logs cache cache/videos cache/events config data/faces

# 创建非root用户
RUN groupadd -r cerberus && useradd -r -g cerberus -G video cerberus

# 设置权限
RUN chown -R cerberus:cerberus /app && \
    chmod +x /app/app.py

# 设置NPU设备权限
RUN echo 'SUBSYSTEM=="ax_npu", GROUP="video", MODE="0666"' > /etc/udev/rules.d/99-ax-npu.rules

# 切换到非root用户
USER cerberus

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# 启动命令
CMD ["python", "app.py"]
