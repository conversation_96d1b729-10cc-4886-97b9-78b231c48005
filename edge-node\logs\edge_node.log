2025-07-12 05:20:53,093 - app.core.node_manager - INFO - 节点管理器初始化完成: edge-node-1
2025-07-12 05:20:53,094 - __main__ - ERROR - 边缘节点启动失败: 'Config' object has no attribute 'get'
2025-07-12 05:26:38,971 - app.core.node_manager - INFO - 节点管理器初始化完成: edge-node-1
2025-07-12 05:26:39,215 - app.ai.detection_engine - WARNING - EnhancedFaceRecognitionDetector 导入失败
2025-07-12 05:26:39,230 - app.ai.detection_engine - ERROR - 目标检测模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/yolov8n.onnx failed:Load model models/yolov8n.onnx failed. File doesn't exist
2025-07-12 05:26:39,231 - app.ai.detection_engine - ERROR - 检测器 object_detection 模型加载失败
2025-07-12 05:26:39,248 - app.ai.detection_engine - ERROR - 人脸识别模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/face_recognition.onnx failed:Load model models/face_recognition.onnx failed. File doesn't exist
2025-07-12 05:26:39,249 - app.ai.detection_engine - ERROR - 检测器 face_recognition 模型加载失败
2025-07-12 05:26:39,250 - app.ai.detection_engine - INFO - 越线检测器初始化完成
2025-07-12 05:26:39,250 - app.ai.detection_engine - INFO - 检测器 line_crossing 初始化成功
2025-07-12 05:26:39,251 - app.ai.detection_engine - INFO - 摄像头遮挡检测器初始化完成
2025-07-12 05:26:39,252 - app.ai.detection_engine - INFO - 检测器 camera_obstruction 初始化成功
2025-07-12 05:26:39,252 - app.ai.detection_engine - INFO - 检测引擎初始化完成，已加载 2 个检测器
2025-07-12 05:26:39,253 - app.video.video_recorder - INFO - 视频文件清理服务已启动
2025-07-12 05:26:39,254 - app.video.video_recorder - INFO - 录制管理器初始化完成
2025-07-12 05:26:39,255 - app.video.stream_manager - INFO - 视频流管理器初始化完成
2025-07-12 05:26:39,278 - app.ai.detection_engine - WARNING - EnhancedFaceRecognitionDetector 导入失败
2025-07-12 05:26:39,282 - app.ai.detection_engine - ERROR - 目标检测模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/yolov8n.onnx failed:Load model models/yolov8n.onnx failed. File doesn't exist
2025-07-12 05:26:39,283 - app.ai.detection_engine - ERROR - 检测器 object_detection 模型加载失败
2025-07-12 05:26:39,296 - app.ai.detection_engine - ERROR - 人脸识别模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/face_recognition.onnx failed:Load model models/face_recognition.onnx failed. File doesn't exist
2025-07-12 05:26:39,297 - app.ai.detection_engine - ERROR - 检测器 face_recognition 模型加载失败
2025-07-12 05:26:39,298 - app.ai.detection_engine - INFO - 越线检测器初始化完成
2025-07-12 05:26:39,298 - app.ai.detection_engine - INFO - 检测器 line_crossing 初始化成功
2025-07-12 05:26:39,298 - app.ai.detection_engine - INFO - 摄像头遮挡检测器初始化完成
2025-07-12 05:26:39,299 - app.ai.detection_engine - INFO - 检测器 camera_obstruction 初始化成功
2025-07-12 05:26:39,300 - app.ai.detection_engine - INFO - 检测引擎初始化完成，已加载 2 个检测器
2025-07-12 05:26:39,301 - app.mqtt.publisher - INFO - MQTT客户端初始化完成: cerberus_edge_edge-node-1_1752297999
2025-07-12 05:26:39,301 - app.mqtt.publisher - INFO - MQTT发布器初始化完成
2025-07-12 05:26:39,302 - app.storage.local_cache - INFO - 本地缓存管理器初始化完成
2025-07-12 05:26:39,302 - app.storage.minio_client - INFO - MinIO客户端初始化完成
2025-07-12 05:26:39,305 - __main__ - INFO - Starting Cerberus Edge Node: edge-node-1
2025-07-12 05:26:39,305 - __main__ - INFO - Initializing components...
2025-07-12 05:26:39,358 - app.storage.local_cache - INFO - 数据库初始化完成
2025-07-12 05:26:39,359 - app.storage.local_cache - INFO - 缓存清理服务已启动
2025-07-12 05:26:39,361 - app.storage.local_cache - INFO - 本地缓存初始化完成
2025-07-12 05:26:39,361 - app.mqtt.publisher - INFO - 连接MQTT代理: mqtt:1883
2025-07-12 05:26:39,364 - app.mqtt.publisher - INFO - 消息队列处理器已启动
2025-07-12 05:26:39,364 - app.mqtt.publisher - INFO - MQTT连接成功: mqtt:1883
2025-07-12 05:26:39,374 - app.storage.minio_client - INFO - 创建存储桶: cerberus-videos
2025-07-12 05:26:39,375 - app.storage.minio_client - INFO - MinIO上传服务已启动
2025-07-12 05:26:39,375 - app.storage.minio_client - INFO - MinIO客户端初始化成功: minio:9000
2025-07-12 05:26:39,376 - app.ai.detection_engine - INFO - 越线检测器初始化完成
2025-07-12 05:26:39,376 - app.ai.detection_engine - INFO - 检测器 line_crossing 模型重新加载成功
2025-07-12 05:26:39,376 - app.ai.detection_engine - INFO - 摄像头遮挡检测器初始化完成
2025-07-12 05:26:39,377 - app.ai.detection_engine - INFO - 检测器 camera_obstruction 模型重新加载成功
2025-07-12 05:26:39,424 - app.core.node_manager - ERROR - 节点注册失败: 405 - {"detail":"Method Not Allowed"}
2025-07-12 05:26:39,425 - app.video.stream_manager - INFO - 摄像头流初始化完成: camera_1
2025-07-12 05:26:39,426 - app.video.stream_manager - INFO - 摄像头添加成功: camera_1
2025-07-12 05:26:39,426 - app.video.stream_manager - INFO - 摄像头流初始化完成: camera_2
2025-07-12 05:26:39,426 - app.video.stream_manager - INFO - 摄像头添加成功: camera_2
2025-07-12 05:26:39,427 - __main__ - INFO - All components initialized successfully
2025-07-12 05:26:39,427 - __main__ - INFO - Starting background threads...
2025-07-12 05:26:39,428 - __main__ - INFO - Started 3 background threads
2025-07-12 05:26:39,437 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-07-12 05:26:39,438 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 05:26:44,174 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:26:44] "GET /health HTTP/1.1" 200 -
2025-07-12 05:27:15,266 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:27:15] "GET /health HTTP/1.1" 200 -
2025-07-12 05:27:46,336 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:27:46] "GET /health HTTP/1.1" 200 -
2025-07-12 05:28:17,404 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:28:17] "GET /health HTTP/1.1" 200 -
2025-07-12 05:28:48,476 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:28:48] "GET /health HTTP/1.1" 200 -
2025-07-12 05:29:19,542 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:29:19] "GET /health HTTP/1.1" 200 -
2025-07-12 05:29:50,600 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:29:50] "GET /health HTTP/1.1" 200 -
2025-07-12 05:30:21,674 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:30:21] "GET /health HTTP/1.1" 200 -
2025-07-12 05:30:52,745 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:30:52] "GET /health HTTP/1.1" 200 -
2025-07-12 05:31:23,819 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:31:23] "GET /health HTTP/1.1" 200 -
2025-07-12 05:31:54,896 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:31:54] "GET /health HTTP/1.1" 200 -
2025-07-12 05:32:25,958 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:32:25] "GET /health HTTP/1.1" 200 -
2025-07-12 05:32:57,018 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:32:57] "GET /health HTTP/1.1" 200 -
2025-07-12 05:33:28,100 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:33:28] "GET /health HTTP/1.1" 200 -
2025-07-12 05:33:59,166 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:33:59] "GET /health HTTP/1.1" 200 -
2025-07-12 05:34:30,238 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:34:30] "GET /health HTTP/1.1" 200 -
2025-07-12 05:35:01,307 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:35:01] "GET /health HTTP/1.1" 200 -
2025-07-12 05:35:32,380 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:35:32] "GET /health HTTP/1.1" 200 -
2025-07-12 05:36:03,440 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:36:03] "GET /health HTTP/1.1" 200 -
2025-07-12 05:36:34,519 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:36:34] "GET /health HTTP/1.1" 200 -
2025-07-12 05:37:05,603 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:37:05] "GET /health HTTP/1.1" 200 -
2025-07-12 05:37:36,689 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:37:36] "GET /health HTTP/1.1" 200 -
2025-07-12 05:38:07,752 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:38:07] "GET /health HTTP/1.1" 200 -
2025-07-12 05:38:38,810 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:38:38] "GET /health HTTP/1.1" 200 -
2025-07-12 05:39:09,858 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:39:09] "GET /health HTTP/1.1" 200 -
2025-07-12 05:39:40,938 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:39:40] "GET /health HTTP/1.1" 200 -
2025-07-12 05:40:12,021 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:40:12] "GET /health HTTP/1.1" 200 -
2025-07-12 05:40:43,100 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:40:43] "GET /health HTTP/1.1" 200 -
2025-07-12 05:41:14,190 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:41:14] "GET /health HTTP/1.1" 200 -
2025-07-12 05:41:45,272 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:41:45] "GET /health HTTP/1.1" 200 -
2025-07-12 05:42:16,353 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:42:16] "GET /health HTTP/1.1" 200 -
2025-07-12 05:42:47,446 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:42:47] "GET /health HTTP/1.1" 200 -
2025-07-12 05:43:18,529 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:43:18] "GET /health HTTP/1.1" 200 -
2025-07-12 05:43:49,640 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:43:49] "GET /health HTTP/1.1" 200 -
2025-07-12 05:43:51,238 - __main__ - INFO - 收到信号 15，开始优雅关闭...
2025-07-12 05:43:51,238 - __main__ - INFO - Stopping Cerberus Edge Node...
2025-07-12 05:43:51,238 - app.video.stream_manager - INFO - 开始清理视频流管理器
2025-07-12 05:43:51,239 - app.video.stream_manager - INFO - 停止所有摄像头
2025-07-12 05:43:51,239 - app.video.stream_manager - INFO - 停止摄像头流: camera_1
2025-07-12 05:43:51,240 - app.video.stream_manager - INFO - 摄像头流已停止: camera_1
2025-07-12 05:43:51,240 - app.video.stream_manager - INFO - 停止摄像头流: camera_2
2025-07-12 05:43:51,240 - app.video.stream_manager - INFO - 摄像头流已停止: camera_2
2025-07-12 05:43:56,241 - app.video.video_recorder - INFO - 视频文件清理服务已停止
2025-07-12 05:43:56,243 - app.video.video_recorder - INFO - 录制管理器清理完成
2025-07-12 05:43:56,243 - app.ai.detection_engine - INFO - 检测引擎资源清理完成
2025-07-12 05:43:56,244 - app.video.stream_manager - INFO - 视频流管理器清理完成
2025-07-12 05:43:57,246 - app.mqtt.publisher - INFO - MQTT正常断开连接
2025-07-12 05:43:57,247 - app.mqtt.publisher - INFO - MQTT连接已断开
2025-07-12 05:44:02,504 - app.core.node_manager - INFO - 节点管理器初始化完成: edge-node-1
2025-07-12 05:44:02,630 - app.ai.detection_engine - WARNING - EnhancedFaceRecognitionDetector 导入失败
2025-07-12 05:44:02,636 - app.ai.detection_engine - ERROR - 目标检测模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/yolov8n.onnx failed:Load model models/yolov8n.onnx failed. File doesn't exist
2025-07-12 05:44:02,636 - app.ai.detection_engine - ERROR - 检测器 object_detection 模型加载失败
2025-07-12 05:44:02,648 - app.ai.detection_engine - ERROR - 人脸识别模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/face_recognition.onnx failed:Load model models/face_recognition.onnx failed. File doesn't exist
2025-07-12 05:44:02,649 - app.ai.detection_engine - ERROR - 检测器 face_recognition 模型加载失败
2025-07-12 05:44:02,650 - app.ai.detection_engine - INFO - 越线检测器初始化完成
2025-07-12 05:44:02,650 - app.ai.detection_engine - INFO - 检测器 line_crossing 初始化成功
2025-07-12 05:44:02,651 - app.ai.detection_engine - INFO - 摄像头遮挡检测器初始化完成
2025-07-12 05:44:02,652 - app.ai.detection_engine - INFO - 检测器 camera_obstruction 初始化成功
2025-07-12 05:44:02,652 - app.ai.detection_engine - INFO - 检测引擎初始化完成，已加载 2 个检测器
2025-07-12 05:44:02,653 - app.video.video_recorder - INFO - 视频文件清理服务已启动
2025-07-12 05:44:02,654 - app.video.video_recorder - INFO - 录制管理器初始化完成
2025-07-12 05:44:02,655 - app.video.stream_manager - INFO - 视频流管理器初始化完成
2025-07-12 05:44:02,680 - app.ai.detection_engine - WARNING - EnhancedFaceRecognitionDetector 导入失败
2025-07-12 05:44:02,683 - app.ai.detection_engine - ERROR - 目标检测模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/yolov8n.onnx failed:Load model models/yolov8n.onnx failed. File doesn't exist
2025-07-12 05:44:02,683 - app.ai.detection_engine - ERROR - 检测器 object_detection 模型加载失败
2025-07-12 05:44:02,695 - app.ai.detection_engine - ERROR - 人脸识别模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/face_recognition.onnx failed:Load model models/face_recognition.onnx failed. File doesn't exist
2025-07-12 05:44:02,695 - app.ai.detection_engine - ERROR - 检测器 face_recognition 模型加载失败
2025-07-12 05:44:02,696 - app.ai.detection_engine - INFO - 越线检测器初始化完成
2025-07-12 05:44:02,697 - app.ai.detection_engine - INFO - 检测器 line_crossing 初始化成功
2025-07-12 05:44:02,698 - app.ai.detection_engine - INFO - 摄像头遮挡检测器初始化完成
2025-07-12 05:44:02,698 - app.ai.detection_engine - INFO - 检测器 camera_obstruction 初始化成功
2025-07-12 05:44:02,699 - app.ai.detection_engine - INFO - 检测引擎初始化完成，已加载 2 个检测器
2025-07-12 05:44:02,700 - app.mqtt.publisher - INFO - MQTT客户端初始化完成: cerberus_edge_edge-node-1_1752299042
2025-07-12 05:44:02,700 - app.mqtt.publisher - INFO - MQTT发布器初始化完成
2025-07-12 05:44:02,701 - app.storage.local_cache - INFO - 本地缓存管理器初始化完成
2025-07-12 05:44:02,702 - app.storage.minio_client - INFO - MinIO客户端初始化完成
2025-07-12 05:44:02,705 - __main__ - INFO - Starting Cerberus Edge Node: edge-node-1
2025-07-12 05:44:02,706 - __main__ - INFO - Initializing components...
2025-07-12 05:44:02,707 - app.storage.local_cache - INFO - 数据库初始化完成
2025-07-12 05:44:02,708 - app.storage.local_cache - INFO - 缓存清理服务已启动
2025-07-12 05:44:02,709 - app.storage.local_cache - INFO - 本地缓存初始化完成
2025-07-12 05:44:02,710 - app.mqtt.publisher - INFO - 连接MQTT代理: mqtt:1883
2025-07-12 05:44:02,712 - app.mqtt.publisher - INFO - 消息队列处理器已启动
2025-07-12 05:44:02,712 - app.mqtt.publisher - INFO - MQTT连接成功: mqtt:1883
2025-07-12 05:44:02,716 - app.storage.minio_client - INFO - MinIO上传服务已启动
2025-07-12 05:44:02,716 - app.storage.minio_client - INFO - MinIO客户端初始化成功: minio:9000
2025-07-12 05:44:02,717 - app.ai.detection_engine - INFO - 越线检测器初始化完成
2025-07-12 05:44:02,717 - app.ai.detection_engine - INFO - 检测器 line_crossing 模型重新加载成功
2025-07-12 05:44:02,718 - app.ai.detection_engine - INFO - 摄像头遮挡检测器初始化完成
2025-07-12 05:44:02,718 - app.ai.detection_engine - INFO - 检测器 camera_obstruction 模型重新加载成功
2025-07-12 05:44:02,758 - app.core.node_manager - ERROR - 节点注册失败: 405 - {"detail":"Method Not Allowed"}
2025-07-12 05:44:02,759 - app.video.stream_manager - INFO - 摄像头流初始化完成: camera_1
2025-07-12 05:44:02,759 - app.video.stream_manager - INFO - 摄像头添加成功: camera_1
2025-07-12 05:44:02,759 - app.video.stream_manager - INFO - 摄像头流初始化完成: camera_2
2025-07-12 05:44:02,760 - app.video.stream_manager - INFO - 摄像头添加成功: camera_2
2025-07-12 05:44:02,761 - __main__ - INFO - All components initialized successfully
2025-07-12 05:44:02,761 - __main__ - INFO - Starting background threads...
2025-07-12 05:44:02,762 - __main__ - INFO - Started 3 background threads
2025-07-12 05:44:02,770 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-07-12 05:44:02,770 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 05:44:07,679 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:44:07] "GET /health HTTP/1.1" 200 -
2025-07-12 05:44:38,784 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:44:38] "GET /health HTTP/1.1" 200 -
2025-07-12 05:44:39,419 - __main__ - INFO - 收到信号 15，开始优雅关闭...
2025-07-12 05:44:39,420 - __main__ - INFO - Stopping Cerberus Edge Node...
2025-07-12 05:44:39,420 - app.video.stream_manager - INFO - 开始清理视频流管理器
2025-07-12 05:44:39,421 - app.video.stream_manager - INFO - 停止所有摄像头
2025-07-12 05:44:39,421 - app.video.stream_manager - INFO - 停止摄像头流: camera_1
2025-07-12 05:44:39,422 - app.video.stream_manager - INFO - 摄像头流已停止: camera_1
2025-07-12 05:44:39,422 - app.video.stream_manager - INFO - 停止摄像头流: camera_2
2025-07-12 05:44:39,423 - app.video.stream_manager - INFO - 摄像头流已停止: camera_2
2025-07-12 05:44:44,424 - app.video.video_recorder - INFO - 视频文件清理服务已停止
2025-07-12 05:44:44,425 - app.video.video_recorder - INFO - 录制管理器清理完成
2025-07-12 05:44:44,425 - app.ai.detection_engine - INFO - 检测引擎资源清理完成
2025-07-12 05:44:44,426 - app.video.stream_manager - INFO - 视频流管理器清理完成
2025-07-12 05:44:45,429 - app.mqtt.publisher - INFO - MQTT正常断开连接
2025-07-12 05:44:45,430 - app.mqtt.publisher - INFO - MQTT连接已断开
2025-07-12 05:44:51,159 - app.core.node_manager - INFO - 节点管理器初始化完成: edge-node-1
2025-07-12 05:44:51,255 - app.ai.detection_engine - WARNING - EnhancedFaceRecognitionDetector 导入失败
2025-07-12 05:44:51,266 - app.ai.detection_engine - ERROR - 目标检测模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/yolov8n.onnx failed:Load model models/yolov8n.onnx failed. File doesn't exist
2025-07-12 05:44:51,267 - app.ai.detection_engine - ERROR - 检测器 object_detection 模型加载失败
2025-07-12 05:44:51,279 - app.ai.detection_engine - ERROR - 人脸识别模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/face_recognition.onnx failed:Load model models/face_recognition.onnx failed. File doesn't exist
2025-07-12 05:44:51,280 - app.ai.detection_engine - ERROR - 检测器 face_recognition 模型加载失败
2025-07-12 05:44:51,280 - app.ai.detection_engine - INFO - 越线检测器初始化完成
2025-07-12 05:44:51,281 - app.ai.detection_engine - INFO - 检测器 line_crossing 初始化成功
2025-07-12 05:44:51,281 - app.ai.detection_engine - INFO - 摄像头遮挡检测器初始化完成
2025-07-12 05:44:51,282 - app.ai.detection_engine - INFO - 检测器 camera_obstruction 初始化成功
2025-07-12 05:44:51,282 - app.ai.detection_engine - INFO - 检测引擎初始化完成，已加载 2 个检测器
2025-07-12 05:44:51,284 - app.video.video_recorder - INFO - 视频文件清理服务已启动
2025-07-12 05:44:51,284 - app.video.video_recorder - INFO - 录制管理器初始化完成
2025-07-12 05:44:51,285 - app.video.stream_manager - INFO - 视频流管理器初始化完成
2025-07-12 05:44:51,310 - app.ai.detection_engine - WARNING - EnhancedFaceRecognitionDetector 导入失败
2025-07-12 05:44:51,316 - app.ai.detection_engine - ERROR - 目标检测模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/yolov8n.onnx failed:Load model models/yolov8n.onnx failed. File doesn't exist
2025-07-12 05:44:51,317 - app.ai.detection_engine - ERROR - 检测器 object_detection 模型加载失败
2025-07-12 05:44:51,331 - app.ai.detection_engine - ERROR - 人脸识别模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/face_recognition.onnx failed:Load model models/face_recognition.onnx failed. File doesn't exist
2025-07-12 05:44:51,332 - app.ai.detection_engine - ERROR - 检测器 face_recognition 模型加载失败
2025-07-12 05:44:51,333 - app.ai.detection_engine - INFO - 越线检测器初始化完成
2025-07-12 05:44:51,333 - app.ai.detection_engine - INFO - 检测器 line_crossing 初始化成功
2025-07-12 05:44:51,334 - app.ai.detection_engine - INFO - 摄像头遮挡检测器初始化完成
2025-07-12 05:44:51,334 - app.ai.detection_engine - INFO - 检测器 camera_obstruction 初始化成功
2025-07-12 05:44:51,335 - app.ai.detection_engine - INFO - 检测引擎初始化完成，已加载 2 个检测器
2025-07-12 05:44:51,336 - app.mqtt.publisher - INFO - MQTT客户端初始化完成: cerberus_edge_edge-node-1_1752299091
2025-07-12 05:44:51,336 - app.mqtt.publisher - INFO - MQTT发布器初始化完成
2025-07-12 05:44:51,337 - app.storage.local_cache - INFO - 本地缓存管理器初始化完成
2025-07-12 05:44:51,337 - app.storage.minio_client - INFO - MinIO客户端初始化完成
2025-07-12 05:44:51,340 - __main__ - INFO - Starting Cerberus Edge Node: edge-node-1
2025-07-12 05:44:51,341 - __main__ - INFO - Initializing components...
2025-07-12 05:44:51,343 - app.storage.local_cache - INFO - 数据库初始化完成
2025-07-12 05:44:51,344 - app.storage.local_cache - INFO - 缓存清理服务已启动
2025-07-12 05:44:51,344 - app.storage.local_cache - INFO - 本地缓存初始化完成
2025-07-12 05:44:51,345 - app.mqtt.publisher - INFO - 连接MQTT代理: mqtt:1883
2025-07-12 05:44:51,347 - app.mqtt.publisher - INFO - 消息队列处理器已启动
2025-07-12 05:44:51,347 - app.mqtt.publisher - INFO - MQTT连接成功: mqtt:1883
2025-07-12 05:44:51,351 - app.storage.minio_client - INFO - MinIO上传服务已启动
2025-07-12 05:44:51,352 - app.storage.minio_client - INFO - MinIO客户端初始化成功: minio:9000
2025-07-12 05:44:51,352 - app.ai.detection_engine - INFO - 越线检测器初始化完成
2025-07-12 05:44:51,353 - app.ai.detection_engine - INFO - 检测器 line_crossing 模型重新加载成功
2025-07-12 05:44:51,353 - app.ai.detection_engine - INFO - 摄像头遮挡检测器初始化完成
2025-07-12 05:44:51,354 - app.ai.detection_engine - INFO - 检测器 camera_obstruction 模型重新加载成功
2025-07-12 05:44:51,398 - app.core.node_manager - ERROR - 节点注册失败: 405 - {"detail":"Method Not Allowed"}
2025-07-12 05:44:51,399 - app.video.stream_manager - INFO - 摄像头流初始化完成: camera_1
2025-07-12 05:44:51,400 - app.video.stream_manager - INFO - 摄像头添加成功: camera_1
2025-07-12 05:44:51,401 - app.video.stream_manager - INFO - 摄像头流初始化完成: camera_2
2025-07-12 05:44:51,402 - app.video.stream_manager - INFO - 摄像头添加成功: camera_2
2025-07-12 05:44:51,402 - __main__ - INFO - All components initialized successfully
2025-07-12 05:44:51,403 - __main__ - INFO - Starting background threads...
2025-07-12 05:44:51,404 - __main__ - INFO - Started 3 background threads
2025-07-12 05:44:51,414 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-07-12 05:44:51,415 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 05:44:56,388 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:44:56] "GET /health HTTP/1.1" 200 -
2025-07-12 05:45:27,485 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:45:27] "GET /health HTTP/1.1" 200 -
2025-07-12 05:45:58,580 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:45:58] "GET /health HTTP/1.1" 200 -
2025-07-12 05:45:59,516 - werkzeug - INFO - ********** - - [12/Jul/2025 05:45:59] "GET /health HTTP/1.1" 200 -
2025-07-12 05:46:22,557 - werkzeug - INFO - ********** - - [12/Jul/2025 05:46:22] "GET /status HTTP/1.1" 200 -
2025-07-12 05:46:29,762 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:46:29] "GET /health HTTP/1.1" 200 -
2025-07-12 05:46:43,928 - werkzeug - INFO - ********** - - [12/Jul/2025 05:46:43] "[33mGET / HTTP/1.1[0m" 404 -
2025-07-12 05:46:44,040 - werkzeug - INFO - ********** - - [12/Jul/2025 05:46:44] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-12 05:47:00,873 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:47:00] "GET /health HTTP/1.1" 200 -
2025-07-12 05:47:31,962 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:47:31] "GET /health HTTP/1.1" 200 -
2025-07-12 05:48:03,041 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:48:03] "GET /health HTTP/1.1" 200 -
2025-07-12 05:48:33,590 - __main__ - INFO - 收到信号 15，开始优雅关闭...
2025-07-12 05:48:33,592 - __main__ - INFO - Stopping Cerberus Edge Node...
2025-07-12 05:48:33,596 - app.video.stream_manager - INFO - 开始清理视频流管理器
2025-07-12 05:48:33,597 - app.video.stream_manager - INFO - 停止所有摄像头
2025-07-12 05:48:33,597 - app.video.stream_manager - INFO - 停止摄像头流: camera_1
2025-07-12 05:48:33,598 - app.video.stream_manager - INFO - 摄像头流已停止: camera_1
2025-07-12 05:48:33,598 - app.video.stream_manager - INFO - 停止摄像头流: camera_2
2025-07-12 05:48:33,598 - app.video.stream_manager - INFO - 摄像头流已停止: camera_2
2025-07-12 05:48:34,130 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:48:34] "GET /health HTTP/1.1" 200 -
2025-07-12 05:48:38,599 - app.video.video_recorder - INFO - 视频文件清理服务已停止
2025-07-12 05:48:38,599 - app.video.video_recorder - INFO - 录制管理器清理完成
2025-07-12 05:48:38,599 - app.ai.detection_engine - INFO - 检测引擎资源清理完成
2025-07-12 05:48:38,600 - app.video.stream_manager - INFO - 视频流管理器清理完成
2025-07-12 05:48:39,602 - app.mqtt.publisher - INFO - MQTT正常断开连接
2025-07-12 05:48:39,603 - app.mqtt.publisher - INFO - MQTT连接已断开
2025-07-12 05:48:45,074 - app.core.node_manager - INFO - 节点管理器初始化完成: edge-node-1
2025-07-12 05:48:45,174 - app.ai.detection_engine - WARNING - EnhancedFaceRecognitionDetector 导入失败
2025-07-12 05:48:45,187 - app.ai.detection_engine - ERROR - 目标检测模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/yolov8n.onnx failed:Load model models/yolov8n.onnx failed. File doesn't exist
2025-07-12 05:48:45,188 - app.ai.detection_engine - ERROR - 检测器 object_detection 模型加载失败
2025-07-12 05:48:45,201 - app.ai.detection_engine - ERROR - 人脸识别模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/face_recognition.onnx failed:Load model models/face_recognition.onnx failed. File doesn't exist
2025-07-12 05:48:45,202 - app.ai.detection_engine - ERROR - 检测器 face_recognition 模型加载失败
2025-07-12 05:48:45,202 - app.ai.detection_engine - INFO - 越线检测器初始化完成
2025-07-12 05:48:45,203 - app.ai.detection_engine - INFO - 检测器 line_crossing 初始化成功
2025-07-12 05:48:45,204 - app.ai.detection_engine - INFO - 摄像头遮挡检测器初始化完成
2025-07-12 05:48:45,204 - app.ai.detection_engine - INFO - 检测器 camera_obstruction 初始化成功
2025-07-12 05:48:45,205 - app.ai.detection_engine - INFO - 检测引擎初始化完成，已加载 2 个检测器
2025-07-12 05:48:45,206 - app.video.video_recorder - INFO - 视频文件清理服务已启动
2025-07-12 05:48:45,206 - app.video.video_recorder - INFO - 录制管理器初始化完成
2025-07-12 05:48:45,207 - app.video.stream_manager - INFO - 视频流管理器初始化完成
2025-07-12 05:48:45,233 - app.ai.detection_engine - WARNING - EnhancedFaceRecognitionDetector 导入失败
2025-07-12 05:48:45,237 - app.ai.detection_engine - ERROR - 目标检测模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/yolov8n.onnx failed:Load model models/yolov8n.onnx failed. File doesn't exist
2025-07-12 05:48:45,237 - app.ai.detection_engine - ERROR - 检测器 object_detection 模型加载失败
2025-07-12 05:48:45,250 - app.ai.detection_engine - ERROR - 人脸识别模型加载失败: [ONNXRuntimeError] : 3 : NO_SUCHFILE : Load model from models/face_recognition.onnx failed:Load model models/face_recognition.onnx failed. File doesn't exist
2025-07-12 05:48:45,251 - app.ai.detection_engine - ERROR - 检测器 face_recognition 模型加载失败
2025-07-12 05:48:45,252 - app.ai.detection_engine - INFO - 越线检测器初始化完成
2025-07-12 05:48:45,252 - app.ai.detection_engine - INFO - 检测器 line_crossing 初始化成功
2025-07-12 05:48:45,253 - app.ai.detection_engine - INFO - 摄像头遮挡检测器初始化完成
2025-07-12 05:48:45,254 - app.ai.detection_engine - INFO - 检测器 camera_obstruction 初始化成功
2025-07-12 05:48:45,254 - app.ai.detection_engine - INFO - 检测引擎初始化完成，已加载 2 个检测器
2025-07-12 05:48:45,255 - app.mqtt.publisher - INFO - MQTT客户端初始化完成: cerberus_edge_edge-node-1_1752299325
2025-07-12 05:48:45,255 - app.mqtt.publisher - INFO - MQTT发布器初始化完成
2025-07-12 05:48:45,256 - app.storage.local_cache - INFO - 本地缓存管理器初始化完成
2025-07-12 05:48:45,256 - app.storage.minio_client - INFO - MinIO客户端初始化完成
2025-07-12 05:48:45,259 - __main__ - INFO - Starting Cerberus Edge Node: edge-node-1
2025-07-12 05:48:45,260 - __main__ - INFO - Initializing components...
2025-07-12 05:48:45,261 - app.storage.local_cache - INFO - 数据库初始化完成
2025-07-12 05:48:45,262 - app.storage.local_cache - INFO - 缓存清理服务已启动
2025-07-12 05:48:45,263 - app.storage.local_cache - INFO - 本地缓存初始化完成
2025-07-12 05:48:45,264 - app.mqtt.publisher - INFO - 连接MQTT代理: mqtt:1883
2025-07-12 05:48:45,265 - app.mqtt.publisher - INFO - 消息队列处理器已启动
2025-07-12 05:48:45,266 - app.mqtt.publisher - INFO - MQTT连接成功: mqtt:1883
2025-07-12 05:48:45,270 - app.storage.minio_client - INFO - MinIO上传服务已启动
2025-07-12 05:48:45,270 - app.storage.minio_client - INFO - MinIO客户端初始化成功: minio:9000
2025-07-12 05:48:45,271 - app.ai.detection_engine - INFO - 越线检测器初始化完成
2025-07-12 05:48:45,271 - app.ai.detection_engine - INFO - 检测器 line_crossing 模型重新加载成功
2025-07-12 05:48:45,272 - app.ai.detection_engine - INFO - 摄像头遮挡检测器初始化完成
2025-07-12 05:48:45,273 - app.ai.detection_engine - INFO - 检测器 camera_obstruction 模型重新加载成功
2025-07-12 05:48:45,311 - app.core.node_manager - ERROR - 节点注册失败: 405 - {"detail":"Method Not Allowed"}
2025-07-12 05:48:45,312 - app.video.stream_manager - INFO - 摄像头流初始化完成: camera_1
2025-07-12 05:48:45,312 - app.video.stream_manager - INFO - 摄像头添加成功: camera_1
2025-07-12 05:48:45,313 - app.video.stream_manager - INFO - 摄像头流初始化完成: camera_2
2025-07-12 05:48:45,313 - app.video.stream_manager - INFO - 摄像头添加成功: camera_2
2025-07-12 05:48:45,314 - __main__ - INFO - All components initialized successfully
2025-07-12 05:48:45,314 - __main__ - INFO - Starting background threads...
2025-07-12 05:48:45,315 - __main__ - INFO - Started 3 background threads
2025-07-12 05:48:45,322 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-07-12 05:48:45,323 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 05:48:50,221 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:48:50] "GET /health HTTP/1.1" 200 -
2025-07-12 05:49:01,045 - werkzeug - INFO - ********** - - [12/Jul/2025 05:49:01] "GET / HTTP/1.1" 200 -
2025-07-12 05:49:21,326 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:49:21] "GET /health HTTP/1.1" 200 -
2025-07-12 05:49:26,113 - werkzeug - INFO - ********** - - [12/Jul/2025 05:49:26] "GET /cameras HTTP/1.1" 200 -
2025-07-12 05:49:34,835 - werkzeug - INFO - ********** - - [12/Jul/2025 05:49:34] "GET / HTTP/1.1" 200 -
2025-07-12 05:49:52,427 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:49:52] "GET /health HTTP/1.1" 200 -
2025-07-12 05:50:23,523 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:50:23] "GET /health HTTP/1.1" 200 -
2025-07-12 05:50:54,618 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:50:54] "GET /health HTTP/1.1" 200 -
2025-07-12 05:51:25,713 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:51:25] "GET /health HTTP/1.1" 200 -
2025-07-12 05:51:56,813 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:51:56] "GET /health HTTP/1.1" 200 -
2025-07-12 05:52:27,912 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:52:27] "GET /health HTTP/1.1" 200 -
2025-07-12 05:52:59,003 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:52:59] "GET /health HTTP/1.1" 200 -
2025-07-12 05:53:30,091 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:53:30] "GET /health HTTP/1.1" 200 -
2025-07-12 05:54:01,189 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:54:01] "GET /health HTTP/1.1" 200 -
2025-07-12 05:54:32,270 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:54:32] "GET /health HTTP/1.1" 200 -
2025-07-12 05:55:03,352 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:55:03] "GET /health HTTP/1.1" 200 -
2025-07-12 05:55:34,438 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:55:34] "GET /health HTTP/1.1" 200 -
2025-07-12 05:56:05,515 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:56:05] "GET /health HTTP/1.1" 200 -
2025-07-12 05:56:36,610 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:56:36] "GET /health HTTP/1.1" 200 -
2025-07-12 05:57:07,703 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:57:07] "GET /health HTTP/1.1" 200 -
2025-07-12 05:57:24,298 - werkzeug - INFO - ********** - - [12/Jul/2025 05:57:24] "GET /cameras HTTP/1.1" 200 -
2025-07-12 05:57:38,798 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:57:38] "GET /health HTTP/1.1" 200 -
2025-07-12 05:57:44,466 - werkzeug - INFO - ********** - - [12/Jul/2025 05:57:44] "GET /cameras HTTP/1.1" 200 -
2025-07-12 05:57:56,883 - werkzeug - INFO - ********** - - [12/Jul/2025 05:57:56] "GET /cameras HTTP/1.1" 200 -
2025-07-12 05:58:09,892 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:58:09] "GET /health HTTP/1.1" 200 -
2025-07-12 05:58:21,167 - app.video.stream_manager - ERROR - 摄像头连接失败 camera_1: 无法读取视频帧
2025-07-12 05:58:21,168 - werkzeug - INFO - ********** - - [12/Jul/2025 05:58:21] "POST /cameras/camera_1/start HTTP/1.1" 200 -
2025-07-12 05:58:40,980 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:58:40] "GET /health HTTP/1.1" 200 -
2025-07-12 05:58:42,186 - app.video.stream_manager - ERROR - 摄像头连接失败 camera_2: 无法读取视频帧
2025-07-12 05:58:42,186 - werkzeug - INFO - ********** - - [12/Jul/2025 05:58:42] "POST /cameras/camera_2/start HTTP/1.1" 200 -
2025-07-12 05:59:12,053 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:59:12] "GET /health HTTP/1.1" 200 -
2025-07-12 05:59:43,118 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 05:59:43] "GET /health HTTP/1.1" 200 -
2025-07-12 06:00:14,232 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:00:14] "GET /health HTTP/1.1" 200 -
2025-07-12 06:00:45,301 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:00:45] "GET /health HTTP/1.1" 200 -
2025-07-12 06:01:16,391 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:01:16] "GET /health HTTP/1.1" 200 -
2025-07-12 06:01:47,470 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:01:47] "GET /health HTTP/1.1" 200 -
2025-07-12 06:02:18,558 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:02:18] "GET /health HTTP/1.1" 200 -
2025-07-12 06:02:49,654 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:02:49] "GET /health HTTP/1.1" 200 -
2025-07-12 06:03:20,730 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:03:20] "GET /health HTTP/1.1" 200 -
2025-07-12 06:03:51,823 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:03:51] "GET /health HTTP/1.1" 200 -
2025-07-12 06:04:22,926 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:04:22] "GET /health HTTP/1.1" 200 -
2025-07-12 06:04:54,013 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:04:54] "GET /health HTTP/1.1" 200 -
2025-07-12 06:05:25,099 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:05:25] "GET /health HTTP/1.1" 200 -
2025-07-12 06:05:56,183 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:05:56] "GET /health HTTP/1.1" 200 -
2025-07-12 06:06:27,261 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:06:27] "GET /health HTTP/1.1" 200 -
2025-07-12 06:06:58,349 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:06:58] "GET /health HTTP/1.1" 200 -
2025-07-12 06:07:29,445 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:07:29] "GET /health HTTP/1.1" 200 -
2025-07-12 06:08:00,538 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:08:00] "GET /health HTTP/1.1" 200 -
2025-07-12 06:08:31,628 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:08:31] "GET /health HTTP/1.1" 200 -
2025-07-12 06:09:02,721 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:09:02] "GET /health HTTP/1.1" 200 -
2025-07-12 06:09:33,816 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:09:33] "GET /health HTTP/1.1" 200 -
2025-07-12 06:10:04,903 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:10:04] "GET /health HTTP/1.1" 200 -
2025-07-12 06:10:35,975 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:10:35] "GET /health HTTP/1.1" 200 -
2025-07-12 06:11:07,057 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:11:07] "GET /health HTTP/1.1" 200 -
2025-07-12 06:11:38,145 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:11:38] "GET /health HTTP/1.1" 200 -
2025-07-12 06:12:09,254 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:12:09] "GET /health HTTP/1.1" 200 -
2025-07-12 06:12:40,349 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:12:40] "GET /health HTTP/1.1" 200 -
2025-07-12 06:13:11,438 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:13:11] "GET /health HTTP/1.1" 200 -
2025-07-12 06:13:42,535 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:13:42] "GET /health HTTP/1.1" 200 -
2025-07-12 06:14:13,612 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:14:13] "GET /health HTTP/1.1" 200 -
2025-07-12 06:14:44,710 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:14:44] "GET /health HTTP/1.1" 200 -
2025-07-12 06:15:15,789 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:15:15] "GET /health HTTP/1.1" 200 -
2025-07-12 06:15:46,865 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:15:46] "GET /health HTTP/1.1" 200 -
2025-07-12 06:16:17,951 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:16:17] "GET /health HTTP/1.1" 200 -
2025-07-12 06:16:49,038 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:16:49] "GET /health HTTP/1.1" 200 -
2025-07-12 06:17:20,115 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:17:20] "GET /health HTTP/1.1" 200 -
2025-07-12 06:17:51,215 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:17:51] "GET /health HTTP/1.1" 200 -
2025-07-12 06:18:22,311 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:18:22] "GET /health HTTP/1.1" 200 -
2025-07-12 06:18:53,402 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:18:53] "GET /health HTTP/1.1" 200 -
2025-07-12 06:19:24,495 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:19:24] "GET /health HTTP/1.1" 200 -
2025-07-12 06:19:55,584 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:19:55] "GET /health HTTP/1.1" 200 -
2025-07-12 06:20:26,678 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:20:26] "GET /health HTTP/1.1" 200 -
2025-07-12 06:20:57,792 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:20:57] "GET /health HTTP/1.1" 200 -
2025-07-12 06:21:17,185 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:21:17] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:21:25,615 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:21:25] "GET /config HTTP/1.1" 200 -
2025-07-12 06:21:28,920 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:21:28] "GET /health HTTP/1.1" 200 -
2025-07-12 06:22:00,078 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:22:00] "GET /health HTTP/1.1" 200 -
2025-07-12 06:22:20,718 - app.video.stream_manager - ERROR - 摄像头连接失败 camera_1: 无法读取视频帧
2025-07-12 06:22:20,718 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:22:20] "POST /cameras/camera_1/start HTTP/1.1" 200 -
2025-07-12 06:22:26,763 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:22:26] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:22:26,765 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:22:26] "GET /config HTTP/1.1" 200 -
2025-07-12 06:22:26,767 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:22:26] "GET /status HTTP/1.1" 200 -
2025-07-12 06:22:31,173 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:22:31] "GET /health HTTP/1.1" 200 -
2025-07-12 06:22:43,638 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:22:43] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:22:43,658 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:22:43] "GET /config HTTP/1.1" 200 -
2025-07-12 06:22:43,681 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:22:43] "GET /status HTTP/1.1" 200 -
2025-07-12 06:22:58,073 - app.video.stream_manager - ERROR - 摄像头连接失败 camera_1: 无法读取视频帧
2025-07-12 06:22:58,075 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:22:58] "POST /cameras/camera_1/start HTTP/1.1" 200 -
2025-07-12 06:23:02,279 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:23:02] "GET /health HTTP/1.1" 200 -
2025-07-12 06:23:16,872 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:23:16] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:23:16,873 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:23:16] "GET /config HTTP/1.1" 200 -
2025-07-12 06:23:16,874 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:23:16] "GET /status HTTP/1.1" 200 -
2025-07-12 06:23:19,094 - app.video.stream_manager - ERROR - 摄像头连接失败 camera_1: 无法读取视频帧
2025-07-12 06:23:19,095 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:23:19] "POST /cameras/camera_1/start HTTP/1.1" 200 -
2025-07-12 06:23:28,100 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:23:28] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:23:28,101 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:23:28] "GET /config HTTP/1.1" 200 -
2025-07-12 06:23:28,103 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:23:28] "GET /status HTTP/1.1" 200 -
2025-07-12 06:23:33,396 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:23:33] "GET /health HTTP/1.1" 200 -
2025-07-12 06:23:40,136 - app.video.stream_manager - ERROR - 摄像头连接失败 camera_1: 无法读取视频帧
2025-07-12 06:23:40,144 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:23:40] "POST /cameras/camera_1/start HTTP/1.1" 200 -
2025-07-12 06:24:01,153 - app.video.stream_manager - ERROR - 摄像头连接失败 camera_1: 无法读取视频帧
2025-07-12 06:24:01,154 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:24:01] "POST /cameras/camera_1/start HTTP/1.1" 200 -
2025-07-12 06:24:04,466 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:24:04] "GET /health HTTP/1.1" 200 -
2025-07-12 06:24:22,179 - app.video.stream_manager - ERROR - 摄像头连接失败 camera_2: 无法读取视频帧
2025-07-12 06:24:22,180 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:24:22] "POST /cameras/camera_2/start HTTP/1.1" 200 -
2025-07-12 06:24:35,588 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:24:35] "GET /health HTTP/1.1" 200 -
2025-07-12 06:24:36,648 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:24:36] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:24:36,649 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:24:36] "GET /config HTTP/1.1" 200 -
2025-07-12 06:24:36,651 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:24:36] "GET /status HTTP/1.1" 200 -
2025-07-12 06:24:43,239 - app.video.stream_manager - ERROR - 摄像头连接失败 camera_1: 无法读取视频帧
2025-07-12 06:24:43,242 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:24:43] "POST /cameras/camera_1/start HTTP/1.1" 200 -
2025-07-12 06:25:06,683 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:25:06] "GET /health HTTP/1.1" 200 -
2025-07-12 06:25:37,771 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:25:37] "GET /health HTTP/1.1" 200 -
2025-07-12 06:26:08,857 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:26:08] "GET /health HTTP/1.1" 200 -
2025-07-12 06:26:39,958 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:26:39] "GET /health HTTP/1.1" 200 -
2025-07-12 06:27:04,859 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:27:04] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:27:04,860 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:27:04] "GET /config HTTP/1.1" 200 -
2025-07-12 06:27:04,862 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:27:04] "GET /status HTTP/1.1" 200 -
2025-07-12 06:27:11,053 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:27:11] "GET /health HTTP/1.1" 200 -
2025-07-12 06:27:42,179 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:27:42] "GET /health HTTP/1.1" 200 -
2025-07-12 06:28:13,276 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:28:13] "GET /health HTTP/1.1" 200 -
2025-07-12 06:28:44,366 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:28:44] "GET /health HTTP/1.1" 200 -
2025-07-12 06:29:15,446 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:29:15] "GET /health HTTP/1.1" 200 -
2025-07-12 06:29:40,553 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:29:40] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:29:40,554 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:29:40] "GET /config HTTP/1.1" 200 -
2025-07-12 06:29:40,556 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:29:40] "GET /status HTTP/1.1" 200 -
2025-07-12 06:29:46,563 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:29:46] "GET /health HTTP/1.1" 200 -
2025-07-12 06:30:10,551 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:30:10] "GET /config HTTP/1.1" 200 -
2025-07-12 06:30:10,552 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:30:10] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:30:10,555 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:30:10] "GET /status HTTP/1.1" 200 -
2025-07-12 06:30:17,662 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:30:17] "GET /health HTTP/1.1" 200 -
2025-07-12 06:30:40,552 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:30:40] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:30:40,553 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:30:40] "GET /config HTTP/1.1" 200 -
2025-07-12 06:30:40,556 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:30:40] "GET /status HTTP/1.1" 200 -
2025-07-12 06:30:48,766 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:30:48] "GET /health HTTP/1.1" 200 -
2025-07-12 06:31:10,546 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:31:10] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:31:10,547 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:31:10] "GET /config HTTP/1.1" 200 -
2025-07-12 06:31:10,548 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:31:10] "GET /status HTTP/1.1" 200 -
2025-07-12 06:31:19,869 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:31:19] "GET /health HTTP/1.1" 200 -
2025-07-12 06:31:40,549 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:31:40] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:31:40,549 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:31:40] "GET /config HTTP/1.1" 200 -
2025-07-12 06:31:40,550 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:31:40] "GET /status HTTP/1.1" 200 -
2025-07-12 06:31:50,963 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:31:50] "GET /health HTTP/1.1" 200 -
2025-07-12 06:32:10,547 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:32:10] "GET /config HTTP/1.1" 200 -
2025-07-12 06:32:10,548 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:32:10] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:32:10,549 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:32:10] "GET /status HTTP/1.1" 200 -
2025-07-12 06:32:22,060 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:32:22] "GET /health HTTP/1.1" 200 -
2025-07-12 06:32:40,546 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:32:40] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:32:40,547 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:32:40] "GET /config HTTP/1.1" 200 -
2025-07-12 06:32:40,547 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:32:40] "GET /status HTTP/1.1" 200 -
2025-07-12 06:32:53,151 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:32:53] "GET /health HTTP/1.1" 200 -
2025-07-12 06:33:10,552 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:33:10] "GET /config HTTP/1.1" 200 -
2025-07-12 06:33:10,553 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:33:10] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:33:10,553 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:33:10] "GET /status HTTP/1.1" 200 -
2025-07-12 06:33:24,247 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:33:24] "GET /health HTTP/1.1" 200 -
2025-07-12 06:33:40,551 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:33:40] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:33:40,551 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:33:40] "GET /config HTTP/1.1" 200 -
2025-07-12 06:33:40,552 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:33:40] "GET /status HTTP/1.1" 200 -
2025-07-12 06:33:55,347 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:33:55] "GET /health HTTP/1.1" 200 -
2025-07-12 06:34:10,557 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:34:10] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:34:10,558 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:34:10] "GET /config HTTP/1.1" 200 -
2025-07-12 06:34:10,559 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:34:10] "GET /status HTTP/1.1" 200 -
2025-07-12 06:34:26,442 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:34:26] "GET /health HTTP/1.1" 200 -
2025-07-12 06:34:40,576 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:34:40] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:34:40,577 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:34:40] "GET /config HTTP/1.1" 200 -
2025-07-12 06:34:40,578 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:34:40] "GET /status HTTP/1.1" 200 -
2025-07-12 06:34:57,532 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:34:57] "GET /health HTTP/1.1" 200 -
2025-07-12 06:35:10,554 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:35:10] "GET /config HTTP/1.1" 200 -
2025-07-12 06:35:10,554 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:35:10] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:35:10,555 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:35:10] "GET /status HTTP/1.1" 200 -
2025-07-12 06:35:28,635 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:35:28] "GET /health HTTP/1.1" 200 -
2025-07-12 06:35:40,557 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:35:40] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:35:40,557 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:35:40] "GET /config HTTP/1.1" 200 -
2025-07-12 06:35:40,558 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:35:40] "GET /status HTTP/1.1" 200 -
2025-07-12 06:35:59,717 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:35:59] "GET /health HTTP/1.1" 200 -
2025-07-12 06:36:10,551 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:36:10] "GET /config HTTP/1.1" 200 -
2025-07-12 06:36:10,551 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:36:10] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:36:10,553 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:36:10] "GET /status HTTP/1.1" 200 -
2025-07-12 06:36:30,808 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:36:30] "GET /health HTTP/1.1" 200 -
2025-07-12 06:36:40,552 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:36:40] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:36:40,553 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:36:40] "GET /config HTTP/1.1" 200 -
2025-07-12 06:36:40,553 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:36:40] "GET /status HTTP/1.1" 200 -
2025-07-12 06:37:01,893 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:37:01] "GET /health HTTP/1.1" 200 -
2025-07-12 06:37:10,546 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:37:10] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:37:10,547 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:37:10] "GET /config HTTP/1.1" 200 -
2025-07-12 06:37:10,547 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:37:10] "GET /status HTTP/1.1" 200 -
2025-07-12 06:37:32,987 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:37:32] "GET /health HTTP/1.1" 200 -
2025-07-12 06:37:40,549 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:37:40] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:37:40,550 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:37:40] "GET /config HTTP/1.1" 200 -
2025-07-12 06:37:40,550 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:37:40] "GET /status HTTP/1.1" 200 -
2025-07-12 06:38:04,071 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:38:04] "GET /health HTTP/1.1" 200 -
2025-07-12 06:38:10,551 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:38:10] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:38:10,552 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:38:10] "GET /config HTTP/1.1" 200 -
2025-07-12 06:38:10,553 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:38:10] "GET /status HTTP/1.1" 200 -
2025-07-12 06:38:35,159 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:38:35] "GET /health HTTP/1.1" 200 -
2025-07-12 06:38:40,551 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:38:40] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:38:40,552 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:38:40] "GET /config HTTP/1.1" 200 -
2025-07-12 06:38:40,553 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:38:40] "GET /status HTTP/1.1" 200 -
2025-07-12 06:39:04,009 - app.video.stream_manager - ERROR - 摄像头连接失败 camera_1: 无法读取视频帧
2025-07-12 06:39:04,010 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:04] "POST /cameras/camera_1/start HTTP/1.1" 200 -
2025-07-12 06:39:06,309 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:39:06] "GET /health HTTP/1.1" 200 -
2025-07-12 06:39:08,279 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:08] "GET /config HTTP/1.1" 200 -
2025-07-12 06:39:08,280 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:08] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:39:08,281 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:08] "GET /status HTTP/1.1" 200 -
2025-07-12 06:39:10,173 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:10] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:39:10,173 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:10] "GET /config HTTP/1.1" 200 -
2025-07-12 06:39:10,175 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:10] "GET /status HTTP/1.1" 200 -
2025-07-12 06:39:10,547 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:10] "GET /config HTTP/1.1" 200 -
2025-07-12 06:39:10,547 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:10] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:39:10,549 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:10] "GET /status HTTP/1.1" 200 -
2025-07-12 06:39:25,033 - app.video.stream_manager - ERROR - 摄像头连接失败 camera_1: 无法读取视频帧
2025-07-12 06:39:25,034 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:25] "POST /cameras/camera_1/start HTTP/1.1" 200 -
2025-07-12 06:39:37,391 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 06:39:37] "GET /health HTTP/1.1" 200 -
2025-07-12 06:39:40,589 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:40] "GET /cameras HTTP/1.1" 200 -
2025-07-12 06:39:40,589 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:40] "GET /config HTTP/1.1" 200 -
2025-07-12 06:39:40,591 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:40] "GET /status HTTP/1.1" 200 -
2025-07-12 06:39:46,066 - app.video.stream_manager - ERROR - 摄像头连接失败 camera_1: 无法读取视频帧
2025-07-12 06:39:46,067 - werkzeug - INFO - 172.18.0.7 - - [12/Jul/2025 06:39:46] "POST /cameras/camera_1/start HTTP/1.1" 200 -
