"""
AX650N NPU优化的AI检测器
利用爱芯元智AX650N的NPU加速AI推理
"""

import os
import cv2
import numpy as np
import logging
from typing import List, Dict, Tuple, Optional
import time

try:
    # AX650N NPU Python SDK
    import ax_npu
    from ax_npu import AXModel, AXEngine
    NPU_AVAILABLE = True
except ImportError:
    NPU_AVAILABLE = False
    logging.warning("AX650N NPU SDK not available, falling back to CPU")

from .base_detector import BaseDetector

logger = logging.getLogger(__name__)

class AX650NDetector(BaseDetector):
    """AX650N NPU优化的检测器"""
    
    def __init__(self, model_path: str, config: Dict):
        super().__init__(model_path, config)
        self.npu_enabled = NPU_AVAILABLE and config.get('use_npu', True)
        self.npu_device_id = config.get('npu_device_id', 0)
        self.npu_cores = config.get('npu_cores', 4)
        
        # NPU模型和引擎
        self.ax_model = None
        self.ax_engine = None
        
        # 性能统计
        self.inference_times = []
        self.npu_utilization = 0.0
        
        self._load_model()
    
    def _load_model(self):
        """加载AX650N优化的模型"""
        try:
            if self.npu_enabled:
                self._load_npu_model()
            else:
                self._load_cpu_model()
                
            logger.info(f"AX650N检测器初始化完成 - NPU: {self.npu_enabled}")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def _load_npu_model(self):
        """加载NPU模型"""
        if not NPU_AVAILABLE:
            raise RuntimeError("AX650N NPU SDK不可用")
        
        # 检查NPU设备
        if not os.path.exists('/dev/ax_npu'):
            raise RuntimeError("NPU设备不可用")
        
        # 初始化NPU引擎
        self.ax_engine = AXEngine(device_id=self.npu_device_id)
        
        # 加载模型文件 (.axmodel格式)
        model_file = self.model_path.replace('.onnx', '.axmodel')
        if not os.path.exists(model_file):
            raise FileNotFoundError(f"AX650N模型文件不存在: {model_file}")
        
        self.ax_model = AXModel(model_file)
        
        # 配置NPU参数
        self.ax_engine.set_core_num(self.npu_cores)
        self.ax_engine.load_model(self.ax_model)
        
        logger.info(f"NPU模型加载成功: {model_file}")
        logger.info(f"NPU核心数: {self.npu_cores}")
        logger.info(f"模型输入尺寸: {self.ax_model.get_input_shape()}")
        logger.info(f"模型输出尺寸: {self.ax_model.get_output_shape()}")
    
    def _load_cpu_model(self):
        """加载CPU模型 (fallback)"""
        import onnxruntime as ort
        
        # 配置ONNX Runtime
        providers = ['CPUExecutionProvider']
        sess_options = ort.SessionOptions()
        sess_options.inter_op_num_threads = 4
        sess_options.intra_op_num_threads = 4
        
        self.ort_session = ort.InferenceSession(
            self.model_path, 
            sess_options=sess_options,
            providers=providers
        )
        
        logger.info(f"CPU模型加载成功: {self.model_path}")
    
    def preprocess(self, image: np.ndarray) -> np.ndarray:
        """图像预处理"""
        # 获取模型输入尺寸
        if self.npu_enabled:
            input_shape = self.ax_model.get_input_shape()
            input_height, input_width = input_shape[2], input_shape[3]
        else:
            input_height, input_width = 640, 640
        
        # 调整图像尺寸
        image_resized = cv2.resize(image, (input_width, input_height))
        
        # 归一化
        image_normalized = image_resized.astype(np.float32) / 255.0
        
        # 转换为NCHW格式
        image_transposed = np.transpose(image_normalized, (2, 0, 1))
        
        # 添加batch维度
        image_batch = np.expand_dims(image_transposed, axis=0)
        
        return image_batch
    
    def inference(self, preprocessed_image: np.ndarray) -> List[np.ndarray]:
        """NPU推理"""
        start_time = time.time()
        
        try:
            if self.npu_enabled:
                outputs = self._npu_inference(preprocessed_image)
            else:
                outputs = self._cpu_inference(preprocessed_image)
            
            # 记录推理时间
            inference_time = (time.time() - start_time) * 1000  # ms
            self.inference_times.append(inference_time)
            
            # 保持最近100次的记录
            if len(self.inference_times) > 100:
                self.inference_times.pop(0)
            
            return outputs
            
        except Exception as e:
            logger.error(f"推理失败: {e}")
            raise
    
    def _npu_inference(self, image: np.ndarray) -> List[np.ndarray]:
        """NPU推理"""
        # 设置输入数据
        self.ax_engine.set_input(0, image)
        
        # 执行推理
        self.ax_engine.run()
        
        # 获取输出
        outputs = []
        for i in range(self.ax_model.get_output_num()):
            output = self.ax_engine.get_output(i)
            outputs.append(output)
        
        return outputs
    
    def _cpu_inference(self, image: np.ndarray) -> List[np.ndarray]:
        """CPU推理 (fallback)"""
        input_name = self.ort_session.get_inputs()[0].name
        outputs = self.ort_session.run(None, {input_name: image})
        return outputs
    
    def postprocess(self, outputs: List[np.ndarray], original_shape: Tuple[int, int]) -> List[Dict]:
        """后处理"""
        detections = []
        
        # 解析输出 (假设是YOLO格式)
        if len(outputs) > 0:
            predictions = outputs[0][0]  # [num_detections, 85] for YOLO
            
            for pred in predictions:
                confidence = pred[4]
                
                if confidence > self.config.get('confidence_threshold', 0.5):
                    # 边界框坐标
                    x_center, y_center, width, height = pred[:4]
                    
                    # 转换为原始图像坐标
                    orig_height, orig_width = original_shape
                    x1 = int((x_center - width/2) * orig_width)
                    y1 = int((y_center - height/2) * orig_height)
                    x2 = int((x_center + width/2) * orig_width)
                    y2 = int((y_center + height/2) * orig_height)
                    
                    # 类别概率
                    class_probs = pred[5:]
                    class_id = np.argmax(class_probs)
                    class_confidence = class_probs[class_id]
                    
                    detection = {
                        'bbox': [x1, y1, x2, y2],
                        'confidence': float(confidence * class_confidence),
                        'class_id': int(class_id),
                        'class_name': self._get_class_name(class_id)
                    }
                    
                    detections.append(detection)
        
        return detections
    
    def _get_class_name(self, class_id: int) -> str:
        """获取类别名称"""
        class_names = [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus',
            'train', 'truck', 'boat', 'traffic light', 'fire hydrant',
            'stop sign', 'parking meter', 'bench', 'bird', 'cat', 'dog',
            'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe',
            'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee'
        ]
        
        if 0 <= class_id < len(class_names):
            return class_names[class_id]
        return f'class_{class_id}'
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        if not self.inference_times:
            return {}
        
        avg_time = np.mean(self.inference_times)
        min_time = np.min(self.inference_times)
        max_time = np.max(self.inference_times)
        fps = 1000.0 / avg_time if avg_time > 0 else 0
        
        stats = {
            'avg_inference_time_ms': round(avg_time, 2),
            'min_inference_time_ms': round(min_time, 2),
            'max_inference_time_ms': round(max_time, 2),
            'fps': round(fps, 2),
            'npu_enabled': self.npu_enabled,
            'npu_cores': self.npu_cores if self.npu_enabled else 0,
            'total_inferences': len(self.inference_times)
        }
        
        if self.npu_enabled:
            stats['npu_utilization'] = self._get_npu_utilization()
        
        return stats
    
    def _get_npu_utilization(self) -> float:
        """获取NPU利用率"""
        try:
            if self.ax_engine:
                return self.ax_engine.get_utilization()
        except:
            pass
        return 0.0
    
    def cleanup(self):
        """清理资源"""
        if self.npu_enabled and self.ax_engine:
            self.ax_engine.release()
        
        if hasattr(self, 'ort_session'):
            del self.ort_session
        
        logger.info("AX650N检测器资源已清理")
