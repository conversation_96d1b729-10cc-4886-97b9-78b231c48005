"""
节点管理端点
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import httpx
import asyncio

from app.core.database import get_db
from app.models.database import Node

router = APIRouter()

@router.get("/")
async def list_nodes(db: AsyncSession = Depends(get_db)):
    """获取所有节点列表"""
    try:
        result = await db.execute(select(Node))
        nodes = result.scalars().all()
        return [
            {
                "id": node.id,
                "node_id": node.node_id,
                "name": node.name,
                "status": node.status,
                "location": node.location,
                "ip_address": node.ip_address,
                "last_heartbeat": node.last_heartbeat.isoformat() if node.last_heartbeat else None,
                "created_at": node.created_at.isoformat(),
                "updated_at": node.updated_at.isoformat()
            }
            for node in nodes
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching nodes: {str(e)}")

@router.get("/{node_id}")
async def get_node(node_id: str, db: AsyncSession = Depends(get_db)):
    """获取特定节点信息"""
    try:
        result = await db.execute(select(Node).where(Node.node_id == node_id))
        node = result.scalar_one_or_none()
        
        if not node:
            raise HTTPException(status_code=404, detail="Node not found")
        
        return {
            "id": node.id,
            "node_id": node.node_id,
            "name": node.name,
            "description": node.description,
            "location": node.location,
            "ip_address": node.ip_address,
            "status": node.status,
            "last_heartbeat": node.last_heartbeat.isoformat() if node.last_heartbeat else None,
            "version": node.version,
            "config": node.config,
            "metadata": node.metadata,
            "created_at": node.created_at.isoformat(),
            "updated_at": node.updated_at.isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching node: {str(e)}")

@router.get("/{node_id}/status")
async def get_node_status(node_id: str, db: AsyncSession = Depends(get_db)):
    """获取节点状态"""
    try:
        result = await db.execute(select(Node.status, Node.last_heartbeat).where(Node.node_id == node_id))
        node_data = result.first()
        
        if not node_data:
            raise HTTPException(status_code=404, detail="Node not found")
        
        return {
            "node_id": node_id,
            "status": node_data.status,
            "last_heartbeat": node_data.last_heartbeat.isoformat() if node_data.last_heartbeat else None
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching node status: {str(e)}")

# 摄像头管理API
@router.get("/{node_id}/cameras")
async def get_node_cameras(node_id: str):
    """获取节点的摄像头列表"""
    try:
        # 直接调用边缘节点API
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:5000/cameras", timeout=10.0)
            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(status_code=502, detail="Failed to fetch cameras from edge node")
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Edge node timeout")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching cameras: {str(e)}")

@router.post("/{node_id}/cameras/{camera_id}/start")
async def start_camera(node_id: str, camera_id: str):
    """启动摄像头"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(f"http://localhost:5000/cameras/{camera_id}/start", timeout=10.0)
            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(status_code=502, detail="Failed to start camera")
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Edge node timeout")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting camera: {str(e)}")

@router.post("/{node_id}/cameras/{camera_id}/stop")
async def stop_camera(node_id: str, camera_id: str):
    """停止摄像头"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(f"http://localhost:5000/cameras/{camera_id}/stop", timeout=10.0)
            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(status_code=502, detail="Failed to stop camera")
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Edge node timeout")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error stopping camera: {str(e)}")

# AI检测器配置API
@router.get("/{node_id}/detectors")
async def get_node_detectors(node_id: str):
    """获取节点的检测器配置"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:5000/config", timeout=10.0)
            if response.status_code == 200:
                config = response.json()
                # 提取检测器信息
                detectors = config.get('detectors', {})
                return {
                    "detectors": detectors,
                    "available_detectors": [
                        {
                            "id": "object_detection",
                            "name": "目标检测",
                            "description": "检测人员、车辆等目标",
                            "enabled": "object_detection" in detectors
                        },
                        {
                            "id": "face_recognition",
                            "name": "人脸识别",
                            "description": "人脸检测和身份识别",
                            "enabled": "face_recognition" in detectors
                        },
                        {
                            "id": "line_crossing",
                            "name": "越线检测",
                            "description": "检测物体穿越预设线条",
                            "enabled": "line_crossing" in detectors
                        },
                        {
                            "id": "camera_obstruction",
                            "name": "摄像头异常检测",
                            "description": "检测摄像头遮挡或异常",
                            "enabled": "camera_obstruction" in detectors
                        }
                    ]
                }
            else:
                raise HTTPException(status_code=502, detail="Failed to fetch detectors from edge node")
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Edge node timeout")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching detectors: {str(e)}")

@router.get("/{node_id}/edge-status")
async def get_edge_node_status(node_id: str):
    """获取边缘节点的详细状态"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:5000/status", timeout=10.0)
            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(status_code=502, detail="Failed to fetch edge node status")
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Edge node timeout")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching edge node status: {str(e)}")
