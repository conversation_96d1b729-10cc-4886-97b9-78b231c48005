#!/bin/bash

# Cerberus智能监控系统 - AX650N边缘节点部署脚本
# 适用于爱芯元智AX650N NPU硬件

set -e

echo "🚀 开始部署Cerberus智能监控系统到AX650N硬件..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查系统环境
check_system() {
    log_step "检查系统环境..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法识别操作系统"
        exit 1
    fi
    
    source /etc/os-release
    log_info "操作系统: $PRETTY_NAME"
    
    # 检查架构
    ARCH=$(uname -m)
    log_info "系统架构: $ARCH"
    
    if [[ "$ARCH" != "aarch64" ]]; then
        log_warn "当前架构不是aarch64，可能不兼容AX650N"
    fi
    
    # 检查NPU设备
    if [[ -e /dev/ax_npu ]]; then
        log_info "✅ 检测到AX650N NPU设备"
    else
        log_warn "⚠️  未检测到NPU设备，请确认驱动已安装"
    fi
}

# 安装系统依赖
install_dependencies() {
    log_step "安装系统依赖..."
    
    # 更新包管理器
    sudo apt update
    
    # 安装基础依赖
    sudo apt install -y \
        curl \
        wget \
        git \
        python3 \
        python3-pip \
        python3-dev \
        build-essential \
        cmake \
        pkg-config \
        libopencv-dev \
        ffmpeg \
        v4l-utils
    
    log_info "✅ 系统依赖安装完成"
}

# 安装Docker
install_docker() {
    log_step "安装Docker..."
    
    if command -v docker &> /dev/null; then
        log_info "Docker已安装: $(docker --version)"
        return
    fi
    
    # 安装Docker
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    
    # 添加用户到docker组
    sudo usermod -aG docker $USER
    
    # 安装Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    # 启动Docker服务
    sudo systemctl enable docker
    sudo systemctl start docker
    
    log_info "✅ Docker安装完成"
    log_warn "请重新登录以使docker组权限生效"
}

# 安装AX650N SDK
install_ax650n_sdk() {
    log_step "安装AX650N NPU SDK..."
    
    SDK_DIR="/opt/ax650n-sdk"
    
    if [[ -d "$SDK_DIR" ]]; then
        log_info "AX650N SDK已安装"
        return
    fi
    
    # 创建SDK目录
    sudo mkdir -p "$SDK_DIR"
    
    # 下载SDK (需要替换为实际下载地址)
    log_info "请从爱芯元智官网下载AX650N SDK并解压到 $SDK_DIR"
    log_info "下载地址: https://www.axera-tech.com/product/T2"
    
    read -p "SDK是否已安装? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_error "请先安装AX650N SDK"
        exit 1
    fi
    
    # 设置环境变量
    echo "export AX_NPU_SDK_PATH=$SDK_DIR" | sudo tee -a /etc/environment
    echo "export LD_LIBRARY_PATH=\$AX_NPU_SDK_PATH/lib:\$LD_LIBRARY_PATH" | sudo tee -a /etc/environment
    echo "export PATH=\$AX_NPU_SDK_PATH/bin:\$PATH" | sudo tee -a /etc/environment
    
    log_info "✅ AX650N SDK配置完成"
}

# 配置系统参数
configure_system() {
    log_step "配置系统参数..."
    
    # 配置NPU设备权限
    echo 'SUBSYSTEM=="ax_npu", GROUP="video", MODE="0666"' | sudo tee /etc/udev/rules.d/99-ax-npu.rules
    
    # 重新加载udev规则
    sudo udevadm control --reload-rules
    sudo udevadm trigger
    
    # 配置内存参数
    echo "vm.swappiness=10" | sudo tee -a /etc/sysctl.conf
    echo "vm.vfs_cache_pressure=50" | sudo tee -a /etc/sysctl.conf
    
    # 应用配置
    sudo sysctl -p
    
    log_info "✅ 系统参数配置完成"
}

# 创建项目目录
setup_project() {
    log_step "设置项目目录..."
    
    PROJECT_DIR="$HOME/cerberus-edge"
    
    # 创建项目目录
    mkdir -p "$PROJECT_DIR"
    cd "$PROJECT_DIR"
    
    # 创建必要的子目录
    mkdir -p {data/cache,logs,models/ax650n,config}
    
    # 设置权限
    chmod 755 "$PROJECT_DIR"
    
    log_info "✅ 项目目录创建完成: $PROJECT_DIR"
    echo "PROJECT_DIR=$PROJECT_DIR" > .env
}

# 下载AI模型
download_models() {
    log_step "下载AI模型..."
    
    MODEL_DIR="$PROJECT_DIR/models/ax650n"
    
    # 创建模型目录
    mkdir -p "$MODEL_DIR"
    
    log_info "请将转换好的AX650N模型文件(.axmodel)放置到: $MODEL_DIR"
    log_info "模型转换工具: AX-ModelConverter"
    
    # 示例模型文件
    cat > "$MODEL_DIR/README.md" << EOF
# AX650N AI模型目录

请将以下模型文件放置在此目录:

1. yolov8n.axmodel - 目标检测模型
2. face_detection.axmodel - 人脸检测模型
3. line_crossing.axmodel - 越线检测模型

模型转换命令示例:
\`\`\`bash
# 使用AX-ModelConverter转换ONNX模型为axmodel
ax_converter --input yolov8n.onnx --output yolov8n.axmodel --target AX650N
\`\`\`

模型下载地址:
- YOLOv8: https://github.com/ultralytics/ultralytics
- 人脸检测: https://github.com/deepinsight/insightface
EOF
    
    log_info "✅ 模型目录配置完成"
}

# 配置摄像头
configure_cameras() {
    log_step "配置摄像头..."
    
    CONFIG_DIR="$PROJECT_DIR/config"
    
    # 创建摄像头配置文件
    cat > "$CONFIG_DIR/cameras.json" << EOF
{
  "camera_1": {
    "name": "前门摄像头",
    "rtsp_url": "rtsp://admin:password@*************:554/stream1",
    "ip_address": "*************",
    "enabled": true,
    "resolution": "1920x1080",
    "fps": 25,
    "location": "主入口",
    "detectors": ["object_detection", "face_recognition"],
    "recording": {
      "enabled": true,
      "duration_minutes": 5,
      "trigger_events": ["person", "vehicle"]
    }
  },
  "camera_2": {
    "name": "停车场摄像头",
    "rtsp_url": "rtsp://admin:password@*************:554/stream1", 
    "ip_address": "*************",
    "enabled": true,
    "resolution": "1920x1080",
    "fps": 25,
    "location": "停车场",
    "detectors": ["object_detection", "line_crossing"],
    "recording": {
      "enabled": true,
      "duration_minutes": 3,
      "trigger_events": ["vehicle", "person"]
    }
  }
}
EOF
    
    log_info "✅ 摄像头配置文件创建完成"
    log_warn "请根据实际情况修改 $CONFIG_DIR/cameras.json 中的RTSP地址"
}

# 部署应用
deploy_application() {
    log_step "部署应用..."
    
    # 复制docker-compose文件
    cp docker-compose.ax650n.yml "$PROJECT_DIR/docker-compose.yml"
    
    # 复制应用代码
    cp -r edge-node "$PROJECT_DIR/"
    
    # 构建镜像
    cd "$PROJECT_DIR"
    docker-compose build
    
    log_info "✅ 应用部署完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    cd "$PROJECT_DIR"
    
    # 启动服务
    docker-compose up -d
    
    # 等待服务启动
    sleep 10
    
    # 检查服务状态
    docker-compose ps
    
    log_info "✅ 服务启动完成"
}

# 验证部署
verify_deployment() {
    log_step "验证部署..."
    
    # 检查API响应
    if curl -f http://localhost:5000/health &> /dev/null; then
        log_info "✅ API服务正常"
    else
        log_error "❌ API服务异常"
        return 1
    fi
    
    # 检查NPU状态
    if curl -s http://localhost:5000/status | grep -q "npu_enabled"; then
        log_info "✅ NPU功能正常"
    else
        log_warn "⚠️  NPU功能可能异常"
    fi
    
    log_info "✅ 部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    log_step "部署信息"
    
    echo "🎉 Cerberus智能监控系统部署完成!"
    echo ""
    echo "📊 服务地址:"
    echo "  - 边缘节点API: http://localhost:5000"
    echo "  - 健康检查: http://localhost:5000/health"
    echo "  - 状态查询: http://localhost:5000/status"
    echo ""
    echo "📁 重要目录:"
    echo "  - 项目目录: $PROJECT_DIR"
    echo "  - 配置文件: $PROJECT_DIR/config/"
    echo "  - 日志文件: $PROJECT_DIR/logs/"
    echo "  - 模型文件: $PROJECT_DIR/models/ax650n/"
    echo ""
    echo "🔧 常用命令:"
    echo "  - 查看日志: docker-compose logs -f"
    echo "  - 重启服务: docker-compose restart"
    echo "  - 停止服务: docker-compose down"
    echo "  - 更新代码: git pull && docker-compose up -d --build"
    echo ""
    echo "📝 下一步:"
    echo "  1. 修改摄像头配置: $PROJECT_DIR/config/cameras.json"
    echo "  2. 放置AI模型文件到: $PROJECT_DIR/models/ax650n/"
    echo "  3. 重启服务使配置生效: docker-compose restart"
}

# 主函数
main() {
    echo "🚀 Cerberus智能监控系统 - AX650N部署脚本"
    echo "================================================"
    
    check_root
    check_system
    install_dependencies
    install_docker
    install_ax650n_sdk
    configure_system
    setup_project
    download_models
    configure_cameras
    deploy_application
    start_services
    verify_deployment
    show_deployment_info
    
    echo ""
    log_info "🎉 部署完成! 系统已准备就绪!"
}

# 执行主函数
main "$@"
