# Cerberus Edge Node Requirements for AX650N

# Web框架
flask==2.3.3
flask-cors==4.0.0

# MQTT通信
paho-mqtt==1.6.1

# 对象存储
minio==7.1.17

# 数据处理
numpy==1.24.3
opencv-python==4.8.0.76

# 视频处理
imageio==2.31.1
imageio-ffmpeg==0.4.8

# AI推理 (AX650N优化版本)
onnxruntime==1.15.1

# 配置管理
python-dotenv==1.0.0
pyyaml==6.0.1

# 日志和监控
loguru==0.7.0
psutil==5.9.5

# 工具库
requests==2.31.0
pillow==10.0.0
tqdm==4.65.0

# 时间处理
python-dateutil==2.8.2

# JSON处理
ujson==5.8.0

# 多线程
concurrent-futures==3.1.1

# AX650N NPU专用库
# 注意：这些库需要从爱芯元智官方获取
# ax-npu-python>=1.0.0
# ax-model-converter>=1.0.0
