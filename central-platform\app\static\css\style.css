/* Cerberus 智能监控系统 - 自定义样式 */

/* 全局样式 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f4f6f9;
    color: #333;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: white !important;
}

.navbar-nav .nav-link.active {
    color: white !important;
    background-color: rgba(255,255,255,0.1);
    border-radius: 5px;
}

/* 主要内容区域 */
.main-content {
    min-height: calc(100vh - 120px);
    padding: 20px 0;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    margin-bottom: 20px;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* 统计卡片 */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.stats-card.bg-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%);
}

.stats-card.bg-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #1e7e34 100%);
}

.stats-card.bg-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%);
    color: #212529;
}

.stats-card.bg-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
}

/* 表格样式 */
.table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 徽章样式 */
.badge {
    font-size: 0.75em;
    padding: 0.35em 0.65em;
    border-radius: 6px;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-online {
    background-color: var(--success-color);
    animation: pulse 2s infinite;
}

.status-offline {
    background-color: var(--danger-color);
}

.status-warning {
    background-color: var(--warning-color);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* 模态框样式 */
.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 10px 10px 0 0;
}

/* 表单样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 页脚样式 */
.footer {
    background-color: var(--dark-color);
    color: white;
    text-align: center;
    padding: 20px 0;
    margin-top: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        padding: 10px 0;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .stats-card {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 图表容器样式 */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* 错误和成功消息样式 */
.alert {
    border-radius: 8px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}
