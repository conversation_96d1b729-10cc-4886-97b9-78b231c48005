# Cerberus智能监控系统 - 快速操作指南

## 🚀 立即开始使用

### 1. 访问系统界面
- **主界面**: http://localhost:9997
- **节点管理**: http://localhost:9997/nodes ⭐ (摄像头和AI配置)
- **事件管理**: http://localhost:9997/events
- **统计分析**: http://localhost:9997/analytics
- **系统设置**: http://localhost:9997/settings

### 2. 🎥 配置和启动摄像头

#### 方法1: 通过Web界面
1. 访问 http://localhost:9997/nodes
2. 在"摄像头配置"区域查看现有摄像头
3. 点击摄像头旁边的"启动"按钮 ▶️

#### 方法2: 通过API命令
```bash
# 启动camera_1
curl -X POST http://localhost:5000/cameras/camera_1/start

# 启动camera_2  
curl -X POST http://localhost:5000/cameras/camera_2/start

# 查看摄像头状态
curl http://localhost:5000/cameras
```

### 3. 🤖 配置AI检测算法

#### 当前可用的检测器：
- **目标检测** (object_detection) - 检测人员、车辆等
- **人脸识别** (face_recognition) - 人脸检测和身份识别  
- **越线检测** (line_crossing) - 检测物体穿越预设线条
- **摄像头异常检测** (camera_obstruction) - 检测摄像头遮挡

#### 配置步骤：
1. 在节点管理页面找到"AI检测算法配置"
2. 点击算法旁边的配置按钮 🔧
3. 调整置信度阈值 (0.1-1.0)
4. 保存配置

### 4. 📊 查看检测结果

#### 实时监控：
1. 访问 http://localhost:9997/events
2. 查看实时检测事件列表
3. 点击"查看详情"按钮查看事件截图

#### 统计分析：
1. 访问 http://localhost:9997/analytics
2. 查看事件趋势图表
3. 分析检测算法性能

### 5. 🔧 常用API操作

#### 检查系统状态：
```bash
# 中央平台健康检查
curl http://localhost:9997/health

# 边缘节点状态
curl http://localhost:5000/status

# 摄像头列表
curl http://localhost:5000/cameras
```

#### 摄像头控制：
```bash
# 启动摄像头
curl -X POST http://localhost:5000/cameras/camera_1/start

# 停止摄像头
curl -X POST http://localhost:5000/cameras/camera_1/stop

# 查看配置
curl http://localhost:5000/config
```

### 6. 📝 配置真实摄像头

#### 编辑配置文件：
文件位置: `edge-node/config/cameras.json`

```json
{
  "camera_1": {
    "name": "前门摄像头",
    "rtsp_url": "rtsp://admin:password@*************:554/stream1",
    "enabled": true,
    "fps": 25,
    "resolution": "1280x720",
    "detectors": ["object_detection", "line_crossing"],
    "auto_reconnect": true
  }
}
```

#### 应用配置：
```bash
# 重启边缘节点
docker-compose restart edge-node-1
```

### 7. 🚨 故障排除

#### 如果摄像头无法启动：
1. 检查RTSP地址是否正确
2. 确认网络连接
3. 查看日志: `docker-compose logs edge-node-1`

#### 如果检测不工作：
1. 确认摄像头已启动
2. 检查检测器配置
3. 查看边缘节点状态: `curl http://localhost:5000/status`

#### 如果界面无法访问：
1. 检查容器状态: `docker-compose ps`
2. 重启服务: `docker-compose restart central-platform`

### 8. 📱 移动端访问

系统支持响应式设计，可以在手机或平板上访问：
- 在移动设备浏览器中访问 http://localhost:9997
- 所有功能都可以正常使用

### 9. 🔐 安全设置

#### 默认访问信息：
- **MinIO存储**: http://localhost:9000
  - 用户名: cerberus
  - 密码: cerberus123
- **MQTT管理**: http://localhost:18083  
  - 用户名: admin
  - 密码: public

### 10. 📈 性能优化建议

#### 提高检测性能：
1. 降低视频分辨率 (720p → 480p)
2. 减少帧率 (25fps → 15fps)
3. 调整检测间隔
4. 禁用不需要的检测器

#### 存储优化：
1. 设置视频保存天数
2. 启用自动清理
3. 压缩视频文件

## 🎯 快速测试流程

1. **启动摄像头**: 访问节点管理页面，点击启动按钮
2. **查看状态**: 刷新页面查看摄像头状态变化
3. **模拟检测**: 如果有真实摄像头，在镜头前移动物体
4. **查看事件**: 访问事件管理页面查看检测结果
5. **分析数据**: 访问统计分析页面查看图表

现在系统完全可用，您可以开始配置和使用智能监控功能了！🎊
