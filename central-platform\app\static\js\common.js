/**
 * Cerberus 智能监控系统 - 通用JavaScript函数
 */

// 全局配置
const CONFIG = {
    API_BASE_URL: '/api/v1',
    REFRESH_INTERVAL: 30000, // 30秒
    TOAST_DURATION: 3000,
    CHART_COLORS: {
        primary: '#007bff',
        success: '#28a745',
        danger: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8',
        secondary: '#6c757d'
    }
};

// 工具函数
const Utils = {
    /**
     * 格式化时间戳
     */
    formatTimestamp: function(timestamp) {
        if (!timestamp) return '未知';
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN');
    },

    /**
     * 格式化文件大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 格式化运行时间
     */
    formatUptime: function(seconds) {
        if (!seconds) return '0秒';
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        let result = '';
        if (days > 0) result += `${days}天 `;
        if (hours > 0) result += `${hours}小时 `;
        if (minutes > 0) result += `${minutes}分钟 `;
        if (secs > 0 || result === '') result += `${secs}秒`;

        return result.trim();
    },

    /**
     * 显示加载状态
     */
    showLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) {
            element.innerHTML = '<span class="loading"></span> 加载中...';
            element.disabled = true;
        }
    },

    /**
     * 隐藏加载状态
     */
    hideLoading: function(element, originalText = '') {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) {
            element.innerHTML = originalText;
            element.disabled = false;
        }
    },

    /**
     * 显示Toast消息
     */
    showToast: function(message, type = 'info') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} position-fixed`;
        toast.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        toast.innerHTML = `
            <strong>${this.getToastTitle(type)}</strong> ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.parentElement.removeChild(toast);
                }
            }, 300);
        }, CONFIG.TOAST_DURATION);
    },

    /**
     * 获取Toast标题
     */
    getToastTitle: function(type) {
        const titles = {
            success: '成功',
            danger: '错误',
            warning: '警告',
            info: '信息'
        };
        return titles[type] || '通知';
    },

    /**
     * 确认对话框
     */
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },

    /**
     * 防抖函数
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// API请求封装
const API = {
    /**
     * 通用请求方法
     */
    request: async function(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(CONFIG.API_BASE_URL + url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    },

    /**
     * GET请求
     */
    get: function(url) {
        return this.request(url, { method: 'GET' });
    },

    /**
     * POST请求
     */
    post: function(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },

    /**
     * PUT请求
     */
    put: function(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },

    /**
     * DELETE请求
     */
    delete: function(url) {
        return this.request(url, { method: 'DELETE' });
    }
};

// 页面通用功能
const PageUtils = {
    /**
     * 初始化页面
     */
    init: function() {
        this.setupEventListeners();
        this.updateActiveNavItem();
    },

    /**
     * 设置事件监听器
     */
    setupEventListeners: function() {
        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
        });

        // 页面可见性变化时刷新数据
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                // 页面变为可见时刷新数据
                if (typeof refreshData === 'function') {
                    refreshData();
                }
            }
        });
    },

    /**
     * 更新导航栏活跃项
     */
    updateActiveNavItem: function() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });
    },

    /**
     * 设置定时刷新
     */
    setupAutoRefresh: function(callback, interval = CONFIG.REFRESH_INTERVAL) {
        if (typeof callback === 'function') {
            setInterval(callback, interval);
        }
    }
};

// 表格工具
const TableUtils = {
    /**
     * 更新表格数据
     */
    updateTable: function(tableId, data, columns) {
        const table = document.getElementById(tableId);
        if (!table) return;

        const tbody = table.querySelector('tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        data.forEach(row => {
            const tr = document.createElement('tr');
            columns.forEach(column => {
                const td = document.createElement('td');
                if (typeof column.render === 'function') {
                    td.innerHTML = column.render(row[column.key], row);
                } else {
                    td.textContent = row[column.key] || '';
                }
                tr.appendChild(td);
            });
            tbody.appendChild(tr);
        });
    },

    /**
     * 创建状态徽章
     */
    createStatusBadge: function(status, text) {
        const badgeClass = this.getStatusBadgeClass(status);
        return `<span class="badge ${badgeClass}">${text}</span>`;
    },

    /**
     * 获取状态徽章样式
     */
    getStatusBadgeClass: function(status) {
        const statusMap = {
            'active': 'badge-success',
            'inactive': 'badge-secondary',
            'online': 'badge-success',
            'offline': 'badge-danger',
            'warning': 'badge-warning',
            'error': 'badge-danger'
        };
        return statusMap[status] || 'badge-secondary';
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    PageUtils.init();
    
    // 初始化Bootstrap工具提示
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
});

// 导出到全局作用域
window.Utils = Utils;
window.API = API;
window.PageUtils = PageUtils;
window.TableUtils = TableUtils;
