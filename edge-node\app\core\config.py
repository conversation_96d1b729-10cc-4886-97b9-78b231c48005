"""
边缘节点配置管理
"""

import os
import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class Config:
    """边缘节点配置类"""
    
    def __init__(self):
        # 基本配置
        self.NODE_ID = os.getenv('NODE_ID', 'edge-node-1')
        self.DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
        
        # Flask配置
        self.FLASK_HOST = os.getenv('FLASK_HOST', '0.0.0.0')
        self.FLASK_PORT = int(os.getenv('FLASK_PORT', '5000'))
        
        # MQTT配置
        self.MQTT_BROKER_HOST = os.getenv('MQTT_BROKER_HOST', 'localhost')
        self.MQTT_BROKER_PORT = int(os.getenv('MQTT_BROKER_PORT', '1883'))
        self.MQTT_USERNAME = os.getenv('MQTT_USERNAME', '')
        self.MQTT_PASSWORD = os.getenv('MQTT_PASSWORD', '')
        self.MQTT_KEEPALIVE = int(os.getenv('MQTT_KEEPALIVE', '60'))
        
        # MinIO配置
        self.MINIO_ENDPOINT = os.getenv('MINIO_ENDPOINT', 'localhost:9000')
        self.MINIO_ACCESS_KEY = os.getenv('MINIO_ACCESS_KEY', 'cerberus')
        self.MINIO_SECRET_KEY = os.getenv('MINIO_SECRET_KEY', 'cerberus123')
        self.MINIO_BUCKET = os.getenv('MINIO_BUCKET', 'cerberus-videos')
        self.MINIO_SECURE = os.getenv('MINIO_SECURE', 'False').lower() == 'true'
        
        # 中央平台配置
        self.CENTRAL_PLATFORM_URL = os.getenv('CENTRAL_PLATFORM_URL', 'http://localhost:8000')
        
        # 视频处理配置
        self.VIDEO_RESOLUTION = os.getenv('VIDEO_RESOLUTION', '1280x720')
        self.VIDEO_FPS = int(os.getenv('VIDEO_FPS', '25'))
        self.VIDEO_CODEC = os.getenv('VIDEO_CODEC', 'h264')
        self.PRE_RECORD_SECONDS = int(os.getenv('PRE_RECORD_SECONDS', '15'))
        self.POST_RECORD_SECONDS = int(os.getenv('POST_RECORD_SECONDS', '15'))
        
        # AI检测配置
        self.AI_MODEL_PATH = os.getenv('AI_MODEL_PATH', 'models/')
        self.DETECTION_CONFIDENCE_THRESHOLD = float(os.getenv('DETECTION_CONFIDENCE_THRESHOLD', '0.5'))
        self.DETECTION_INTERVAL = float(os.getenv('DETECTION_INTERVAL', '1.0'))  # 秒
        
        # 缓存配置
        self.CACHE_DIR = os.getenv('CACHE_DIR', 'cache/')
        self.CACHE_MAX_SIZE_GB = int(os.getenv('CACHE_MAX_SIZE_GB', '10'))
        self.CACHE_CLEANUP_INTERVAL = int(os.getenv('CACHE_CLEANUP_INTERVAL', '3600'))  # 秒
        
        # 状态报告配置
        self.STATUS_REPORT_INTERVAL = int(os.getenv('STATUS_REPORT_INTERVAL', '30'))  # 秒
        self.DATA_SYNC_INTERVAL = int(os.getenv('DATA_SYNC_INTERVAL', '60'))  # 秒
        
        # 摄像头配置
        self.cameras = self._load_camera_config()
        
        # AI检测器配置
        self.detectors = self._load_detector_config()
        
        # 创建必要的目录
        self._create_directories()
    
    def _load_camera_config(self) -> Dict[str, Dict[str, Any]]:
        """加载摄像头配置"""
        config_file = os.getenv('CAMERA_CONFIG_FILE', 'config/cameras.json')
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Failed to load camera config: {e}")
        
        # 默认配置
        return {
            "camera_1": {
                "name": "前门摄像头",
                "rtsp_url": "rtsp://admin:password@192.168.1.100:554/stream1",
                "enabled": True,
                "detection_zones": [
                    {"name": "entrance", "polygon": [[0, 0], [100, 0], [100, 100], [0, 100]]}
                ],
                "detectors": ["object_detection", "face_recognition"]
            }
        }
    
    def _load_detector_config(self) -> Dict[str, Dict[str, Any]]:
        """加载检测器配置"""
        config_file = os.getenv('DETECTOR_CONFIG_FILE', 'config/detectors.json')
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Failed to load detector config: {e}")
        
        # 默认配置
        return {
            "object_detection": {
                "model_path": "models/yolov8n.onnx",
                "confidence_threshold": 0.5,
                "nms_threshold": 0.4,
                "target_classes": ["person", "car", "truck", "bicycle"],
                "enabled": True
            },
            "face_recognition": {
                "model_path": "models/face_recognition.onnx",
                "confidence_threshold": 0.7,
                "face_database": "data/faces/",
                "enabled": True
            },
            "line_crossing": {
                "enabled": True,
                "lines": [
                    {"name": "entrance_line", "start": [100, 200], "end": [300, 200]}
                ]
            },
            "camera_tampering": {
                "enabled": True,
                "sensitivity": 0.8,
                "check_interval": 10
            }
        }
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            'logs',
            'cache',
            'cache/videos',
            'cache/events',
            'models',
            'config',
            'data/faces'
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def get_camera_config(self, camera_id: str) -> Optional[Dict[str, Any]]:
        """获取指定摄像头配置"""
        return self.cameras.get(camera_id)
    
    def get_detector_config(self, detector_name: str) -> Optional[Dict[str, Any]]:
        """获取指定检测器配置"""
        return self.detectors.get(detector_name)
    
    def update_camera_config(self, camera_id: str, config: Dict[str, Any]):
        """更新摄像头配置"""
        self.cameras[camera_id] = config
        self._save_camera_config()
    
    def update_detector_config(self, detector_name: str, config: Dict[str, Any]):
        """更新检测器配置"""
        self.detectors[detector_name] = config
        self._save_detector_config()
    
    def _save_camera_config(self):
        """保存摄像头配置"""
        config_file = os.getenv('CAMERA_CONFIG_FILE', 'config/cameras.json')
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.cameras, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save camera config: {e}")
    
    def _save_detector_config(self):
        """保存检测器配置"""
        config_file = os.getenv('DETECTOR_CONFIG_FILE', 'config/detectors.json')
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.detectors, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save detector config: {e}")
    
    def update(self, new_config: Dict[str, Any]):
        """更新配置"""
        for key, value in new_config.items():
            if hasattr(self, key):
                setattr(self, key, value)
                logger.info(f"Updated config: {key} = {value}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持字典式访问"""
        # 首先尝试从属性获取
        if hasattr(self, key):
            return getattr(self, key)

        # 然后尝试从字典形式的配置获取
        config_dict = self.to_dict()
        return config_dict.get(key, default)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'node_id': self.NODE_ID,
            'debug': self.DEBUG,
            'flask_host': self.FLASK_HOST,
            'flask_port': self.FLASK_PORT,
            'mqtt_broker_host': self.MQTT_BROKER_HOST,
            'mqtt_broker_port': self.MQTT_BROKER_PORT,
            'minio_endpoint': self.MINIO_ENDPOINT,
            'minio_bucket': self.MINIO_BUCKET,
            'central_platform_url': self.CENTRAL_PLATFORM_URL,
            'video_resolution': self.VIDEO_RESOLUTION,
            'video_fps': self.VIDEO_FPS,
            'pre_record_seconds': self.PRE_RECORD_SECONDS,
            'post_record_seconds': self.POST_RECORD_SECONDS,
            'detection_confidence_threshold': self.DETECTION_CONFIDENCE_THRESHOLD,
            'detection_interval': self.DETECTION_INTERVAL,
            'status_report_interval': self.STATUS_REPORT_INTERVAL,
            'data_sync_interval': self.DATA_SYNC_INTERVAL,
            'cameras': self.cameras,
            'detectors': self.detectors,
            # 添加一些常用的配置键
            'VIDEO_FPS': self.VIDEO_FPS,
            'VIDEO_RESOLUTION': self.VIDEO_RESOLUTION,
            'PRE_RECORD_SECONDS': self.PRE_RECORD_SECONDS,
            'POST_RECORD_SECONDS': self.POST_RECORD_SECONDS,
            'VIDEO_CODEC': self.VIDEO_CODEC,
            'CACHE_DIR': self.CACHE_DIR,
            'CACHE_MAX_SIZE_GB': self.CACHE_MAX_SIZE_GB,
            'VIDEO_RETENTION_HOURS': 24,  # 默认值
            'DELETE_AFTER_UPLOAD': False  # 默认值
        }
