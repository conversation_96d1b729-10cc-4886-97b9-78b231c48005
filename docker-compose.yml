version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15
    container_name: cerberus-postgres
    environment:
      POSTGRES_DB: cerberus
      POSTGRES_USER: cerberus
      POSTGRES_PASSWORD: cerberus123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
      - ./infrastructure/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - cerberus-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: cerberus-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cerberus-network

  # MQTT Broker (EMQ X)
  mqtt:
    image: emqx/emqx:5.1
    container_name: cerberus-mqtt
    ports:
      - "1883:1883"      # MQTT
      - "8083:8083"      # WebSocket
      - "18083:18083"    # Dashboard
    environment:
      EMQX_NAME: cerberus-mqtt
      EMQX_HOST: 127.0.0.1
    volumes:
      - mqtt_data:/opt/emqx/data
      - mqtt_log:/opt/emqx/log
    networks:
      - cerberus-network

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: cerberus-minio
    ports:
      - "9000:9000"      # API
      - "9001:9001"      # Console
    environment:
      MINIO_ROOT_USER: cerberus
      MINIO_ROOT_PASSWORD: cerberus123
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - cerberus-network

  # 中央管理平台
  central-platform:
    build:
      context: ./central-platform
      dockerfile: Dockerfile
    container_name: cerberus-central
    ports:
      - "9997:8000"
    environment:
      DATABASE_URL: ***********************************************/cerberus
      REDIS_URL: redis://redis:6379/0
      MQTT_BROKER_HOST: mqtt
      MQTT_BROKER_PORT: 1883
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: cerberus
      MINIO_SECRET_KEY: cerberus123
    depends_on:
      - postgres
      - redis
      - mqtt
      - minio
    volumes:
      - ./central-platform:/app
    networks:
      - cerberus-network

  # 边缘节点示例
  edge-node-1:
    build:
      context: ./edge-node
      dockerfile: Dockerfile
    container_name: cerberus-edge-1
    environment:
      NODE_ID: edge-node-1
      MQTT_BROKER_HOST: mqtt
      MQTT_BROKER_PORT: 1883
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: cerberus
      MINIO_SECRET_KEY: cerberus123
      CENTRAL_PLATFORM_URL: http://central-platform:8000
    depends_on:
      - mqtt
      - minio
      - central-platform
    volumes:
      - ./edge-node:/app
      - edge_cache_1:/app/cache
    networks:
      - cerberus-network

volumes:
  postgres_data:
  redis_data:
  mqtt_data:
  mqtt_log:
  minio_data:
  edge_cache_1:

networks:
  cerberus-network:
    driver: bridge
