{% extends "base.html" %}

{% block title %}节点管理 - Cerberus监控系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-server"></i>
                        边缘节点管理
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" onclick="refreshNodes()">
                            <i class="fas fa-sync"></i>
                            刷新
                        </button>
                        <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#addNodeModal">
                            <i class="fas fa-plus"></i>
                            添加节点
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="nodesTable">
                            <thead>
                                <tr>
                                    <th>节点ID</th>
                                    <th>节点名称</th>
                                    <th>状态</th>
                                    <th>IP地址</th>
                                    <th>摄像头数量</th>
                                    <th>运行时间</th>
                                    <th>最后心跳</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="nodesTableBody">
                                <tr>
                                    <td>edge-node-1</td>
                                    <td>边缘节点1</td>
                                    <td><span class="badge badge-success">在线</span></td>
                                    <td>**********</td>
                                    <td>2</td>
                                    <td>2小时30分</td>
                                    <td>刚刚</td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="viewNodeDetails('edge-node-1')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" onclick="configureNode('edge-node-1')">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="restartNode('edge-node-1')">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 节点详情卡片 -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>摄像头配置</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>摄像头ID</th>
                                    <th>名称</th>
                                    <th>状态</th>
                                    <th>RTSP地址</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="camerasTableBody">
                                <tr>
                                    <td>camera_1</td>
                                    <td>前门摄像头</td>
                                    <td><span class="badge badge-secondary">未激活</span></td>
                                    <td>rtsp://192.168.1.100:554/stream1</td>
                                    <td>
                                        <button class="btn btn-sm btn-success" onclick="startCamera('camera_1')">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" onclick="editCamera('camera_1')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>camera_2</td>
                                    <td>后门摄像头</td>
                                    <td><span class="badge badge-secondary">未激活</span></td>
                                    <td>rtsp://192.168.1.101:554/stream1</td>
                                    <td>
                                        <button class="btn btn-sm btn-success" onclick="startCamera('camera_2')">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" onclick="editCamera('camera_2')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <button class="btn btn-primary" data-toggle="modal" data-target="#addCameraModal">
                        <i class="fas fa-plus"></i>
                        添加摄像头
                    </button>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>AI检测算法配置</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>算法名称</th>
                                    <th>状态</th>
                                    <th>置信度</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>目标检测</td>
                                    <td><span class="badge badge-success">启用</span></td>
                                    <td>0.5</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning" onclick="configureDetector('object_detection')">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>人脸识别</td>
                                    <td><span class="badge badge-warning">禁用</span></td>
                                    <td>0.7</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning" onclick="configureDetector('face_recognition')">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>越线检测</td>
                                    <td><span class="badge badge-success">启用</span></td>
                                    <td>0.6</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning" onclick="configureDetector('line_crossing')">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>摄像头异常检测</td>
                                    <td><span class="badge badge-success">启用</span></td>
                                    <td>0.8</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning" onclick="configureDetector('camera_obstruction')">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <button class="btn btn-primary" data-toggle="modal" data-target="#addDetectorModal">
                        <i class="fas fa-plus"></i>
                        添加检测器
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加摄像头模态框 -->
<div class="modal fade" id="addCameraModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">添加摄像头</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addCameraForm">
                    <div class="form-group">
                        <label>摄像头ID</label>
                        <input type="text" class="form-control" name="camera_id" required>
                    </div>
                    <div class="form-group">
                        <label>摄像头名称</label>
                        <input type="text" class="form-control" name="camera_name" required>
                    </div>
                    <div class="form-group">
                        <label>RTSP地址</label>
                        <input type="text" class="form-control" name="rtsp_url" placeholder="rtsp://用户名:密码@IP:端口/路径" required>
                    </div>
                    <div class="form-group">
                        <label>分辨率</label>
                        <select class="form-control" name="resolution">
                            <option value="1920x1080">1920x1080 (1080p)</option>
                            <option value="1280x720" selected>1280x720 (720p)</option>
                            <option value="640x480">640x480 (480p)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>帧率</label>
                        <input type="number" class="form-control" name="fps" value="25" min="1" max="60">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addCamera()">添加</button>
            </div>
        </div>
    </div>
</div>

<script>
// 刷新节点列表
function refreshNodes() {
    // 这里可以通过API获取最新的节点信息
    location.reload();
}

// 启动摄像头
function startCamera(cameraId) {
    fetch(`http://localhost:5000/cameras/${cameraId}/start`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            alert(`摄像头 ${cameraId} 启动成功`);
            refreshNodes();
        } else {
            alert(`启动失败: ${data.message}`);
        }
    })
    .catch(error => {
        alert(`启动失败: ${error.message}`);
    });
}

// 配置检测器
function configureDetector(detectorType) {
    alert(`配置检测器: ${detectorType}`);
    // 这里可以打开配置对话框
}

// 添加摄像头
function addCamera() {
    const form = document.getElementById('addCameraForm');
    const formData = new FormData(form);
    
    // 这里可以通过API添加摄像头
    alert('摄像头添加功能开发中...');
    $('#addCameraModal').modal('hide');
}

// 页面加载时获取实时数据
document.addEventListener('DOMContentLoaded', function() {
    // 获取摄像头状态
    fetch('http://localhost:5000/cameras')
        .then(response => response.json())
        .then(data => {
            updateCameraTable(data);
        })
        .catch(error => console.error('获取摄像头数据失败:', error));
});

function updateCameraTable(cameras) {
    const tbody = document.getElementById('camerasTableBody');
    tbody.innerHTML = '';
    
    Object.keys(cameras).forEach(cameraId => {
        const camera = cameras[cameraId];
        const row = `
            <tr>
                <td>${camera.camera_id}</td>
                <td>${camera.name}</td>
                <td><span class="badge badge-${camera.is_active ? 'success' : 'secondary'}">${camera.is_active ? '运行中' : '未激活'}</span></td>
                <td>配置中...</td>
                <td>
                    <button class="btn btn-sm btn-${camera.is_active ? 'danger' : 'success'}" onclick="${camera.is_active ? 'stopCamera' : 'startCamera'}('${camera.camera_id}')">
                        <i class="fas fa-${camera.is_active ? 'stop' : 'play'}"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="editCamera('${camera.camera_id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}
</script>
{% endblock %}
