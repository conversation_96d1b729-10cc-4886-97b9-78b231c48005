{% extends "base.html" %}

{% block title %}节点管理 - Cerberus监控系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-server"></i>
                        边缘节点管理
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" onclick="refreshNodes()">
                            <i class="fas fa-sync"></i>
                            刷新
                        </button>
                        <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#addNodeModal">
                            <i class="fas fa-plus"></i>
                            添加节点
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="nodesTable">
                            <thead>
                                <tr>
                                    <th>节点ID</th>
                                    <th>节点名称</th>
                                    <th>状态</th>
                                    <th>IP地址</th>
                                    <th>摄像头数量</th>
                                    <th>运行时间</th>
                                    <th>最后心跳</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="nodesTableBody">
                                <tr>
                                    <td>edge-node-1</td>
                                    <td>边缘节点1</td>
                                    <td><span class="badge badge-success">在线</span></td>
                                    <td>**********</td>
                                    <td>2</td>
                                    <td>2小时30分</td>
                                    <td>刚刚</td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="viewNodeDetails('edge-node-1')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" onclick="configureNode('edge-node-1')">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="restartNode('edge-node-1')">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 节点详情卡片 -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>摄像头配置</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>摄像头ID</th>
                                    <th>名称</th>
                                    <th>状态</th>
                                    <th>RTSP地址</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="camerasTableBody">
                                <tr>
                                    <td>camera_1</td>
                                    <td>前门摄像头</td>
                                    <td><span class="badge badge-secondary">未激活</span></td>
                                    <td>rtsp://192.168.1.100:554/stream1</td>
                                    <td>
                                        <button class="btn btn-sm btn-success" onclick="startCamera('camera_1')">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" onclick="editCamera('camera_1')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>camera_2</td>
                                    <td>后门摄像头</td>
                                    <td><span class="badge badge-secondary">未激活</span></td>
                                    <td>rtsp://192.168.1.101:554/stream1</td>
                                    <td>
                                        <button class="btn btn-sm btn-success" onclick="startCamera('camera_2')">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" onclick="editCamera('camera_2')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <button class="btn btn-primary" data-toggle="modal" data-target="#addCameraModal">
                        <i class="fas fa-plus"></i>
                        添加摄像头
                    </button>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>AI检测算法配置</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm" id="detectorsTable">
                            <thead>
                                <tr>
                                    <th>算法名称</th>
                                    <th>状态</th>
                                    <th>置信度</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 检测器数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                    <button class="btn btn-primary" data-toggle="modal" data-target="#addDetectorModal">
                        <i class="fas fa-plus"></i>
                        添加检测器
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加摄像头模态框 -->
<div class="modal fade" id="addCameraModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">添加摄像头</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addCameraForm">
                    <div class="form-group">
                        <label>摄像头ID</label>
                        <input type="text" class="form-control" name="camera_id" required>
                    </div>
                    <div class="form-group">
                        <label>摄像头名称</label>
                        <input type="text" class="form-control" name="camera_name" required>
                    </div>
                    <div class="form-group">
                        <label>RTSP地址</label>
                        <input type="text" class="form-control" name="rtsp_url" placeholder="rtsp://用户名:密码@IP:端口/路径" required>
                    </div>
                    <div class="form-group">
                        <label>分辨率</label>
                        <select class="form-control" name="resolution">
                            <option value="1920x1080">1920x1080 (1080p)</option>
                            <option value="1280x720" selected>1280x720 (720p)</option>
                            <option value="640x480">640x480 (480p)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>帧率</label>
                        <input type="number" class="form-control" name="fps" value="25" min="1" max="60">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addCamera()">添加</button>
            </div>
        </div>
    </div>
</div>

<script>
// 刷新节点列表
function refreshNodes() {
    refreshCameras();
    refreshDetectors();
    refreshNodeStatus();
}

// 启动摄像头
function startCamera(cameraId) {
    const button = event.target;
    const originalText = button.innerHTML;
    Utils.showLoading(button);

    API.post(`/nodes/edge-node-1/cameras/${cameraId}/start`)
    .then(data => {
        if (data.status === 'success') {
            Utils.showToast(`摄像头 ${cameraId} 启动成功`, 'success');
            refreshCameras();
        } else {
            Utils.showToast(`启动失败: ${data.message || '未知错误'}`, 'danger');
        }
    })
    .catch(error => {
        Utils.showToast(`启动失败: ${error.message}`, 'danger');
    })
    .finally(() => {
        Utils.hideLoading(button, originalText);
    });
}

// 停止摄像头
function stopCamera(cameraId) {
    const button = event.target;
    const originalText = button.innerHTML;
    Utils.showLoading(button);

    API.post(`/nodes/edge-node-1/cameras/${cameraId}/stop`)
    .then(data => {
        if (data.status === 'success') {
            Utils.showToast(`摄像头 ${cameraId} 停止成功`, 'success');
            refreshCameras();
        } else {
            Utils.showToast(`停止失败: ${data.message || '未知错误'}`, 'danger');
        }
    })
    .catch(error => {
        Utils.showToast(`停止失败: ${error.message}`, 'danger');
    })
    .finally(() => {
        Utils.hideLoading(button, originalText);
    });
}

// 配置检测器
function configureDetector(detectorType) {
    Utils.showToast(`检测器 ${detectorType} 配置功能开发中...`, 'info');
    // 这里可以打开配置对话框
}

// 添加摄像头
function addCamera() {
    const form = document.getElementById('addCameraForm');
    const formData = new FormData(form);

    // 这里可以通过API添加摄像头
    Utils.showToast('摄像头添加功能开发中...', 'info');
    $('#addCameraModal').modal('hide');
}

// 查看节点详情
function viewNodeDetails(nodeId) {
    Utils.showToast(`查看节点 ${nodeId} 详情功能开发中...`, 'info');
}

// 配置节点
function configureNode(nodeId) {
    Utils.showToast(`配置节点 ${nodeId} 功能开发中...`, 'info');
}

// 重启节点
function restartNode(nodeId) {
    Utils.confirm(`确定要重启节点 ${nodeId} 吗？`, () => {
        Utils.showToast(`重启节点 ${nodeId} 功能开发中...`, 'info');
    });
}

// 编辑摄像头
function editCamera(cameraId) {
    Utils.showToast(`编辑摄像头 ${cameraId} 功能开发中...`, 'info');
}

// 设置自动刷新
function setupAutoRefresh() {
    PageUtils.setupAutoRefresh(() => {
        refreshCameras();
        refreshDetectors();
        refreshNodeStatus();
    }, 30000); // 30秒刷新一次
}

// 页面加载时获取实时数据
document.addEventListener('DOMContentLoaded', function() {
    // 初始化数据
    refreshCameras();
    refreshDetectors();
    refreshNodeStatus();

    // 设置自动刷新
    setupAutoRefresh();

    // 显示欢迎消息
    Utils.showToast('节点管理页面加载完成', 'success');
});

// 刷新摄像头数据
function refreshCameras() {
    API.get('/nodes/edge-node-1/cameras')
        .then(data => {
            updateCameraTable(data);
        })
        .catch(error => {
            console.error('获取摄像头数据失败:', error);
            Utils.showToast('获取摄像头数据失败', 'danger');
        });
}

// 刷新检测器数据
function refreshDetectors() {
    API.get('/nodes/edge-node-1/detectors')
        .then(data => {
            updateDetectorTable(data.available_detectors || []);
        })
        .catch(error => {
            console.error('获取检测器数据失败:', error);
            Utils.showToast('获取检测器数据失败', 'danger');
        });
}

// 刷新节点状态
function refreshNodeStatus() {
    API.get('/nodes/edge-node-1/edge-status')
        .then(data => {
            updateNodeStatusTable(data);
        })
        .catch(error => {
            console.error('获取节点状态失败:', error);
            Utils.showToast('获取节点状态失败', 'danger');
        });
}

function updateCameraTable(cameras) {
    const tbody = document.getElementById('camerasTableBody');
    tbody.innerHTML = '';

    Object.keys(cameras).forEach(cameraId => {
        const camera = cameras[cameraId];
        const row = `
            <tr>
                <td>${camera.camera_id}</td>
                <td>${camera.name}</td>
                <td><span class="badge badge-${camera.is_active ? 'success' : 'secondary'}">${camera.is_active ? '运行中' : '未激活'}</span></td>
                <td>rtsp://配置中...</td>
                <td>
                    <button class="btn btn-sm btn-${camera.is_active ? 'danger' : 'success'}" onclick="${camera.is_active ? 'stopCamera' : 'startCamera'}('${camera.camera_id}')">
                        <i class="fas fa-${camera.is_active ? 'stop' : 'play'}"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="editCamera('${camera.camera_id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

// 更新检测器表格
function updateDetectorTable(detectors) {
    const tbody = document.querySelector('#detectorsTable tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    detectors.forEach(detector => {
        const row = `
            <tr>
                <td>${detector.name}</td>
                <td><span class="badge badge-${detector.enabled ? 'success' : 'warning'}">${detector.enabled ? '启用' : '禁用'}</span></td>
                <td>0.5</td>
                <td>
                    <button class="btn btn-sm btn-warning" onclick="configureDetector('${detector.id}')">
                        <i class="fas fa-cog"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

// 更新节点状态表格
function updateNodeStatusTable(status) {
    const tbody = document.getElementById('nodesTableBody');
    if (!tbody) return;

    const statusBadge = status.mqtt_connected ?
        '<span class="badge badge-success"><i class="status-indicator status-online"></i>在线</span>' :
        '<span class="badge badge-danger"><i class="status-indicator status-offline"></i>离线</span>';

    tbody.innerHTML = `
        <tr>
            <td>${status.node_id || 'edge-node-1'}</td>
            <td>边缘节点1</td>
            <td>${statusBadge}</td>
            <td>**********</td>
            <td>${Object.keys(status.cameras || {}).length}</td>
            <td>${Utils.formatUptime(status.uptime || 0)}</td>
            <td>刚刚</td>
            <td>
                <button class="btn btn-sm btn-info" onclick="viewNodeDetails('${status.node_id}')" title="查看详情">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-warning" onclick="configureNode('${status.node_id}')" title="配置节点">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="restartNode('${status.node_id}')" title="重启节点">
                    <i class="fas fa-redo"></i>
                </button>
            </td>
        </tr>
    `;
}
</script>
{% endblock %}
