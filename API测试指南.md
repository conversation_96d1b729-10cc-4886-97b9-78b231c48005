# Cerberus智能监控系统 - API测试指南

## 🚀 后端API完全可用！

### ✅ 可用的API端点

#### 1. 摄像头管理
```bash
# 获取摄像头列表
curl http://localhost:9997/api/v1/nodes/edge-node-1/cameras

# 启动摄像头
curl -X POST http://localhost:9997/api/v1/nodes/edge-node-1/cameras/camera_1/start

# 停止摄像头
curl -X POST http://localhost:9997/api/v1/nodes/edge-node-1/cameras/camera_1/stop
```

#### 2. AI检测器配置
```bash
# 获取检测器配置
curl http://localhost:9997/api/v1/nodes/edge-node-1/detectors
```

#### 3. 节点状态
```bash
# 获取边缘节点状态
curl http://localhost:9997/api/v1/nodes/edge-node-1/edge-status

# 获取节点列表
curl http://localhost:9997/api/v1/nodes/
```

### 📊 API响应示例

#### 摄像头列表响应：
```json
{
  "camera_1": {
    "camera_id": "camera_1",
    "name": "前门摄像头",
    "is_active": false,
    "status": "inactive",
    "fps": 0.0,
    "frame_count": 0,
    "total_detections": 0,
    "error_count": 0,
    "recording_status": "idle"
  },
  "camera_2": {
    "camera_id": "camera_2", 
    "name": "后门摄像头",
    "is_active": false,
    "status": "inactive",
    "fps": 0.0,
    "frame_count": 0,
    "total_detections": 0,
    "error_count": 0,
    "recording_status": "idle"
  }
}
```

#### 检测器配置响应：
```json
{
  "detectors": {
    "object_detection": {
      "enabled": true,
      "confidence_threshold": 0.5,
      "description": "基于YOLOv8的目标检测",
      "target_classes": ["person", "car", "truck", "bicycle"]
    },
    "face_recognition": {
      "enabled": true,
      "confidence_threshold": 0.7,
      "description": "人脸检测和识别"
    },
    "line_crossing": {
      "enabled": true,
      "description": "越线检测算法",
      "lines": [
        {
          "name": "entrance_line",
          "start": [100, 200],
          "end": [300, 200],
          "direction": "both"
        }
      ]
    },
    "camera_obstruction": {
      "enabled": true,
      "description": "摄像头遮挡和异常检测",
      "sensitivity": 0.8
    }
  },
  "available_detectors": [
    {
      "id": "object_detection",
      "name": "目标检测",
      "description": "检测人员、车辆等目标",
      "enabled": true
    },
    {
      "id": "face_recognition",
      "name": "人脸识别", 
      "description": "人脸检测和身份识别",
      "enabled": true
    },
    {
      "id": "line_crossing",
      "name": "越线检测",
      "description": "检测物体穿越预设线条",
      "enabled": true
    },
    {
      "id": "camera_obstruction",
      "name": "摄像头异常检测",
      "description": "检测摄像头遮挡或异常",
      "enabled": true
    }
  ]
}
```

### 🎯 Web界面功能

#### 访问节点管理页面：http://localhost:9997/nodes

**页面功能：**
1. **实时数据加载** - 页面自动从API获取最新数据
2. **摄像头管理** - 显示摄像头列表，可以启动/停止
3. **AI算法配置** - 显示检测器状态和配置
4. **节点状态监控** - 显示边缘节点运行状态

**交互功能：**
- ✅ 点击"刷新"按钮更新数据
- ✅ 点击摄像头的"启动"按钮开始监控
- ✅ 点击摄像头的"停止"按钮停止监控
- ✅ 点击检测器的"配置"按钮（弹出配置对话框）

### 🔧 测试步骤

#### 1. 测试摄像头功能
```bash
# 1. 查看摄像头状态
curl http://localhost:9997/api/v1/nodes/edge-node-1/cameras

# 2. 启动摄像头（注意：如果没有真实RTSP流会超时）
curl -X POST http://localhost:9997/api/v1/nodes/edge-node-1/cameras/camera_1/start

# 3. 再次查看状态确认变化
curl http://localhost:9997/api/v1/nodes/edge-node-1/cameras
```

#### 2. 测试Web界面
1. 访问 http://localhost:9997/nodes
2. 观察页面是否自动加载数据
3. 点击摄像头的启动按钮
4. 观察状态变化

#### 3. 测试检测器配置
```bash
# 查看检测器配置
curl http://localhost:9997/api/v1/nodes/edge-node-1/detectors | jq
```

### 📝 注意事项

#### 摄像头启动可能超时的原因：
1. **没有真实RTSP流** - 配置文件中的RTSP地址不存在
2. **网络连接问题** - 无法连接到摄像头
3. **配置错误** - RTSP地址格式不正确

#### 解决方案：
1. **配置真实摄像头**：
   ```bash
   # 编辑配置文件
   nano edge-node/config/cameras.json
   
   # 设置真实的RTSP地址
   "rtsp_url": "rtsp://admin:password@*************:554/stream1"
   
   # 重启边缘节点
   docker-compose restart edge-node-1
   ```

2. **使用测试视频文件**：
   ```json
   {
     "camera_1": {
       "name": "测试摄像头",
       "rtsp_url": "/path/to/test_video.mp4",
       "enabled": true
     }
   }
   ```

### 🎊 系统状态总结

- ✅ **后端API** - 完全可用
- ✅ **Web界面** - 完全可用，支持实时数据
- ✅ **摄像头管理** - API和界面都可用
- ✅ **AI检测器配置** - API和界面都可用
- ✅ **实时状态监控** - 完全可用

现在您可以通过Web界面完整地管理摄像头和AI检测算法了！🎉
