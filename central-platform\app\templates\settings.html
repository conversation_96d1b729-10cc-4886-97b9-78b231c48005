{% extends "base.html" %}

{% block title %}系统设置 - Cerberus监控系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cog"></i>
                        系统设置
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 基本设置 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4>基本设置</h4>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="form-group">
                                            <label for="systemName">系统名称</label>
                                            <input type="text" class="form-control" id="systemName" value="Cerberus监控系统">
                                        </div>
                                        <div class="form-group">
                                            <label for="adminEmail">管理员邮箱</label>
                                            <input type="email" class="form-control" id="adminEmail" placeholder="<EMAIL>">
                                        </div>
                                        <div class="form-group">
                                            <label for="timezone">时区设置</label>
                                            <select class="form-control" id="timezone">
                                                <option value="Asia/Shanghai" selected>Asia/Shanghai (UTC+8)</option>
                                                <option value="UTC">UTC (UTC+0)</option>
                                                <option value="America/New_York">America/New_York (UTC-5)</option>
                                            </select>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- 告警设置 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4>告警设置</h4>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="form-group">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="emailAlert" checked>
                                                <label class="form-check-label" for="emailAlert">
                                                    启用邮件告警
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="smsAlert">
                                                <label class="form-check-label" for="smsAlert">
                                                    启用短信告警
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="alertLevel">告警级别</label>
                                            <select class="form-control" id="alertLevel">
                                                <option value="low">低</option>
                                                <option value="medium" selected>中</option>
                                                <option value="high">高</option>
                                            </select>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <!-- 存储设置 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4>存储设置</h4>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="form-group">
                                            <label for="videoRetention">视频保存天数</label>
                                            <input type="number" class="form-control" id="videoRetention" value="30" min="1" max="365">
                                        </div>
                                        <div class="form-group">
                                            <label for="maxStorage">最大存储空间 (GB)</label>
                                            <input type="number" class="form-control" id="maxStorage" value="1000" min="10">
                                        </div>
                                        <div class="form-group">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="autoCleanup" checked>
                                                <label class="form-check-label" for="autoCleanup">
                                                    自动清理过期文件
                                                </label>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- 系统信息 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4>系统信息</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>系统版本:</strong></td>
                                            <td>v1.0.0</td>
                                        </tr>
                                        <tr>
                                            <td><strong>运行时间:</strong></td>
                                            <td id="uptime">计算中...</td>
                                        </tr>
                                        <tr>
                                            <td><strong>活跃节点:</strong></td>
                                            <td id="activeNodes">1</td>
                                        </tr>
                                        <tr>
                                            <td><strong>总摄像头:</strong></td>
                                            <td id="totalCameras">3</td>
                                        </tr>
                                        <tr>
                                            <td><strong>今日事件:</strong></td>
                                            <td id="todayEvents">0</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <button type="button" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                保存设置
                            </button>
                            <button type="button" class="btn btn-secondary ml-2">
                                <i class="fas fa-undo"></i>
                                重置
                            </button>
                            <button type="button" class="btn btn-warning ml-2">
                                <i class="fas fa-sync"></i>
                                重启系统
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 更新运行时间
function updateUptime() {
    // 这里可以通过API获取实际的运行时间
    document.getElementById('uptime').textContent = '2小时30分钟';
}

// 页面加载时更新信息
document.addEventListener('DOMContentLoaded', function() {
    updateUptime();
    
    // 每分钟更新一次运行时间
    setInterval(updateUptime, 60000);
});
</script>
{% endblock %}
