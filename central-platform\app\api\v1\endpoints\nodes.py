"""
节点管理API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
import httpx
import asyncio

from app.core.database import get_db
from app.services.node_service import NodeService

router = APIRouter()

@router.get("/")
async def list_nodes(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取节点列表"""
    try:
        node_service = NodeService()
        nodes = await node_service.get_nodes_list(skip=skip, limit=limit)
        return nodes
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching nodes: {str(e)}")

@router.get("/{node_id}")
async def get_node(node_id: str, db: AsyncSession = Depends(get_db)):
    """获取特定节点信息"""
    try:
        node_service = NodeService()
        node = await node_service.get_node_by_id(node_id)
        
        if not node:
            raise HTTPException(status_code=404, detail="Node not found")
        
        return node
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching node: {str(e)}")

@router.get("/{node_id}/status")
async def get_node_status(node_id: str, db: AsyncSession = Depends(get_db)):
    """获取节点状态"""
    try:
        node_service = NodeService()
        node = await node_service.get_node_by_id(node_id)
        
        if not node:
            raise HTTPException(status_code=404, detail="Node not found")
        
        return {
            "node_id": node_id,
            "status": node["status"],
            "last_heartbeat": node["last_heartbeat"].isoformat() if node["last_heartbeat"] else None
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching node status: {str(e)}")

@router.get("/stats/summary")
async def get_nodes_summary(db: AsyncSession = Depends(get_db)):
    """获取节点统计摘要"""
    try:
        node_service = NodeService()
        health_check = await node_service.check_node_health()
        return health_check
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching nodes summary: {str(e)}")

# 摄像头管理API
@router.get("/{node_id}/cameras")
async def get_node_cameras(node_id: str):
    """获取节点的摄像头列表"""
    try:
        # 直接调用边缘节点API (使用容器名)
        async with httpx.AsyncClient() as client:
            response = await client.get("http://cerberus-edge-1:5000/cameras", timeout=10.0)
            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(status_code=502, detail="Failed to fetch cameras from edge node")
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Edge node timeout")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching cameras: {str(e)}")

@router.post("/{node_id}/cameras/{camera_id}/start")
async def start_camera(node_id: str, camera_id: str):
    """启动摄像头"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(f"http://cerberus-edge-1:5000/cameras/{camera_id}/start", timeout=10.0)
            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(status_code=502, detail="Failed to start camera")
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Edge node timeout")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting camera: {str(e)}")

@router.post("/{node_id}/cameras/{camera_id}/stop")
async def stop_camera(node_id: str, camera_id: str):
    """停止摄像头"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(f"http://cerberus-edge-1:5000/cameras/{camera_id}/stop", timeout=10.0)
            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(status_code=502, detail="Failed to stop camera")
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Edge node timeout")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error stopping camera: {str(e)}")

# AI检测器配置API
@router.get("/{node_id}/detectors")
async def get_node_detectors(node_id: str):
    """获取节点的检测器配置"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://cerberus-edge-1:5000/config", timeout=10.0)
            if response.status_code == 200:
                config = response.json()
                # 提取检测器信息
                detectors = config.get('detectors', {})
                return {
                    "detectors": detectors,
                    "available_detectors": [
                        {
                            "id": "object_detection",
                            "name": "目标检测",
                            "description": "检测人员、车辆等目标",
                            "enabled": "object_detection" in detectors
                        },
                        {
                            "id": "face_recognition",
                            "name": "人脸识别",
                            "description": "人脸检测和身份识别",
                            "enabled": "face_recognition" in detectors
                        },
                        {
                            "id": "line_crossing",
                            "name": "越线检测",
                            "description": "检测物体穿越预设线条",
                            "enabled": "line_crossing" in detectors
                        },
                        {
                            "id": "camera_obstruction",
                            "name": "摄像头异常检测",
                            "description": "检测摄像头遮挡或异常",
                            "enabled": "camera_obstruction" in detectors
                        }
                    ]
                }
            else:
                raise HTTPException(status_code=502, detail="Failed to fetch detectors from edge node")
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Edge node timeout")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching detectors: {str(e)}")

@router.get("/{node_id}/edge-status")
async def get_edge_node_status(node_id: str):
    """获取边缘节点的详细状态"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://cerberus-edge-1:5000/status", timeout=10.0)
            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(status_code=502, detail="Failed to fetch edge node status")
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Edge node timeout")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching edge node status: {str(e)}")
